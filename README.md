# RingCentral Reports Application

A comprehensive PyQt6-based application for analyzing RingCentral EX and CX CSV reports with a modern dark theme interface.

## Features

### 🚀 Core Functionality
- **Automatic Folder Management**: Creates and manages "RC Reports" folder in Documents
- **Multi-Format Processing**: Loads and combines multiple CSV and XLSX files automatically
- **Multi-Sheet Support**: Automatically detects and loads all sheets from Excel files
- **Dark Mode Interface**: Modern, professional dark theme UI
- **Three Main Views**: Dashboard, Reports, and Settings pages

### 📊 Dashboard
- **Data Overview**: Summary cards showing total files, records, users, and date ranges
- **File Information**: Detailed breakdown of each CSV file
- **Data Preview**: Quick preview of combined data

### 📋 Reports
- **Custom Report Builder**: Create and save custom report views
- **Advanced Filtering**: Filter by columns, values, user groups, and date ranges
- **Column Management**: Show/hide specific columns
- **Export Functionality**: Export filtered reports to CSV
- **Saved Reports**: Save and reload report configurations

### ⚙️ Settings
- **User Group Management**: Create and manage user groups for filtering
- **Data Management**: Refresh data, view folder information
- **Application Settings**: Customize display and export preferences

## Installation

### Prerequisites
- Python 3.8 or higher
- Windows OS (currently optimized for Windows)

### Setup
1. Clone or download this repository
2. Install required dependencies:
   ```bash
   pip install -r requirements.txt
   ```

### Dependencies
- PyQt6 (6.6.1) - GUI framework
- pandas (2.1.4) - Data processing
- numpy (1.24.3) - Numerical operations
- openpyxl (3.1.2) - Excel file support

## Usage

### Getting Started
1. Run the application:
   ```bash
   python main.py
   ```

2. **First Launch**: The splash screen will:
   - Check for "RC Reports" folder in your Documents directory
   - Create the folder if it doesn't exist
   - Scan for existing CSV and XLSX files

3. **Add Your Data**:
   - Place your RingCentral CSV or XLSX export files in `Documents/RC Reports/`
   - Use the "Refresh Data" button to reload files

### Working with Data

#### Dashboard
- View overall statistics and data summary
- Check file information and data preview
- Monitor data health and completeness

#### Reports
- **Create New Report**: Click "New Report" to start fresh
- **Apply Filters**: 
  - Select columns to filter by
  - Enter filter values (supports text search)
  - Choose user groups for filtering
- **Manage Columns**: Check/uncheck columns to show/hide
- **Export**: Click "Export to CSV" to save filtered results

#### Settings
- **User Groups**: 
  - Create groups by selecting users from the available list
  - Add/remove users from groups
  - Use groups for filtering in reports
- **Data Management**: 
  - View data folder location
  - Refresh data from CSV files
  - Monitor file statistics

## File Structure

```
RC-Reports/
├── main.py                 # Application entry point
├── requirements.txt        # Python dependencies
├── README.md              # This file
├── gui/                   # User interface modules
│   ├── main_window.py     # Main application window
│   ├── splash_screen.py   # Startup splash screen
│   ├── dashboard.py       # Dashboard page
│   ├── reports.py         # Reports page
│   └── settings.py        # Settings page
├── core/                  # Core functionality
│   ├── data_manager.py    # Data loading and processing
│   ├── report_engine.py   # Report generation
│   └── config_manager.py  # Configuration management
├── utils/                 # Utility functions
│   └── file_utils.py      # File operations
└── styles/                # UI styling
    └── dark_theme.qss     # Dark theme stylesheet
```

## Data Folder Structure

The application creates and uses this folder structure:
```
Documents/
└── RC Reports/            # Main data folder
    ├── *.csv             # Your RingCentral CSV files
    ├── *.xlsx            # Your RingCentral Excel files
    └── config/           # Application configuration
        ├── settings.json      # App settings
        ├── saved_reports.json # Saved report configs
        └── user_groups.json   # User group definitions
```

## Supported Data Types

The application automatically detects and processes:
- **User Information**: Columns containing user, agent, name, employee data
- **Call Data**: Call logs, phone activity, dial records
- **Queue Data**: Queue performance, group statistics
- **Time Data**: Timestamps, dates, duration information
- **Activity Data**: Login times, session data, activity logs
- **Multi-Sheet Excel**: Each sheet in an Excel file is treated as a separate data source

## Multi-Sheet Excel Support

The application automatically handles Excel files with multiple sheets:

- **Automatic Detection**: All sheets in an Excel file are automatically discovered
- **Individual Processing**: Each sheet becomes a separate data source in the application
- **Smart Naming**: Sheets are named as "filename_sheetname" for easy identification
- **Empty Sheet Handling**: Empty sheets are automatically skipped
- **Error Resilience**: If one sheet fails to load, others will still be processed

### Example:
If you have a file called `monthly_reports.xlsx` with sheets named "Calls", "Queues", and "Users":
- The application will create three data sources:
  - `monthly_reports_Calls`
  - `monthly_reports_Queues`
  - `monthly_reports_Users`

## Tips for Best Results

1. **File Naming**: Use descriptive names for your data files (e.g., "call_logs_2024.csv", "queue_stats_jan.xlsx")
2. **Sheet Naming**: Use clear, descriptive sheet names in Excel files (e.g., "Call_Logs", "Queue_Stats")
3. **Data Consistency**: Ensure similar data types use consistent column names across files and sheets
4. **Regular Refresh**: Use the refresh button after adding new data files
5. **User Groups**: Create logical user groups (departments, teams, roles) for better filtering
6. **Save Reports**: Save frequently used report configurations for quick access

## Troubleshooting

### Common Issues
- **No Data Showing**: Ensure CSV or XLSX files are in the correct folder (`Documents/RC Reports/`)
- **Application Won't Start**: Check that all dependencies are installed
- **Slow Performance**: Large data files may take time to load; consider splitting very large files

### Getting Help
- Check the status bar for current application status
- Use the "Refresh Data" button if data seems outdated
- Restart the application if you encounter issues

## Future Enhancements

Planned features for future versions:
- Advanced date range filtering
- Data visualization charts
- Automated report scheduling
- Excel export support
- Custom dashboard widgets
- Data validation and cleaning tools

## Version Information

- **Version**: 1.0.0
- **Last Updated**: 2024
- **Compatibility**: Windows 10/11, Python 3.8+

---

**Note**: This application is designed specifically for RingCentral CSV and XLSX report analysis. Ensure your data files follow standard RingCentral export formats for best results.
