"""
Main Window for RingCentral Reports Application
Contains the main interface with tabbed pages
"""

import os
from PyQt6.QtWidgets import (QMainWindow, QTabWidget, QVBoxLayout, 
                            QWidget, QStatusBar, QMenuBar, QMessageBox)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QAction

from gui.dashboard import DashboardPage
from gui.reports import ReportsPage
from gui.settings import SettingsPage
from core.data_manager import DataManager


class MainWindow(QMainWindow):
    """Main application window"""
    
    def __init__(self):
        super().__init__()
        self.data_manager = DataManager()
        self.setup_ui()
        self.load_stylesheet()
        self.load_initial_data()
    
    def setup_ui(self):
        """Set up the main window UI"""
        self.setWindowTitle("RingCentral Reports - User Analytics Dashboard")
        self.setGeometry(100, 100, 1200, 800)
        
        # Create central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Create tab widget
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # Create pages
        self.dashboard_page = DashboardPage(self.data_manager)
        self.reports_page = ReportsPage(self.data_manager)
        self.settings_page = SettingsPage(self.data_manager)
        
        # Add tabs
        self.tab_widget.addTab(self.dashboard_page, "📊 Dashboard")
        self.tab_widget.addTab(self.reports_page, "📋 Reports")
        self.tab_widget.addTab(self.settings_page, "⚙️ Settings")
        
        # Create menu bar
        self.create_menu_bar()
        
        # Create status bar
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Ready")
        
        # Connect signals
        self.connect_signals()
    
    def create_menu_bar(self):
        """Create the application menu bar"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu('File')
        
        refresh_action = QAction('Refresh Data', self)
        refresh_action.setShortcut('F5')
        refresh_action.triggered.connect(self.refresh_data)
        file_menu.addAction(refresh_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction('Exit', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # View menu
        view_menu = menubar.addMenu('View')
        
        dashboard_action = QAction('Dashboard', self)
        dashboard_action.setShortcut('Ctrl+1')
        dashboard_action.triggered.connect(lambda: self.tab_widget.setCurrentIndex(0))
        view_menu.addAction(dashboard_action)
        
        reports_action = QAction('Reports', self)
        reports_action.setShortcut('Ctrl+2')
        reports_action.triggered.connect(lambda: self.tab_widget.setCurrentIndex(1))
        view_menu.addAction(reports_action)
        
        settings_action = QAction('Settings', self)
        settings_action.setShortcut('Ctrl+3')
        settings_action.triggered.connect(lambda: self.tab_widget.setCurrentIndex(2))
        view_menu.addAction(settings_action)
        
        # Help menu
        help_menu = menubar.addMenu('Help')
        
        about_action = QAction('About', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def connect_signals(self):
        """Connect signals between components"""
        # Connect data refresh signals
        self.settings_page.data_refreshed.connect(self.on_data_refreshed)
        
        # Connect tab change signal
        self.tab_widget.currentChanged.connect(self.on_tab_changed)
    
    def load_stylesheet(self):
        """Load the dark theme stylesheet"""
        try:
            style_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 
                                    'styles', 'dark_theme.qss')
            if os.path.exists(style_path):
                with open(style_path, 'r') as f:
                    self.setStyleSheet(f.read())
        except Exception as e:
            print(f"Error loading stylesheet: {e}")
    
    def load_initial_data(self):
        """Load initial data when the application starts"""
        self.status_bar.showMessage("Loading data...")
        
        success = self.data_manager.load_all_data()
        
        if success:
            summary = self.data_manager.get_data_summary()
            message = f"Loaded {summary['total_files']} files with {summary['total_rows']} total rows"
            self.status_bar.showMessage(message)
            
            # Refresh all pages with new data
            self.refresh_all_pages()
        else:
            self.status_bar.showMessage("No data files found - Add CSV or XLSX files to Documents/RC Reports folder")
    
    def refresh_data(self):
        """Refresh all data from CSV files"""
        self.status_bar.showMessage("Refreshing data...")
        
        success = self.data_manager.load_all_data()
        
        if success:
            summary = self.data_manager.get_data_summary()
            message = f"Refreshed: {summary['total_files']} files with {summary['total_rows']} total rows"
            self.status_bar.showMessage(message)
            
            # Refresh all pages
            self.refresh_all_pages()
        else:
            self.status_bar.showMessage("No data files found")
            QMessageBox.warning(self, "No Data",
                              "No data files found in Documents/RC Reports folder.\n"
                              "Please add your RingCentral CSV or XLSX reports to that folder and refresh.")
    
    def refresh_all_pages(self):
        """Refresh all pages with updated data"""
        self.dashboard_page.refresh_data()
        self.reports_page.refresh_data()
        self.settings_page.refresh_data()
    
    def on_data_refreshed(self):
        """Handle data refresh signal from settings page"""
        self.refresh_all_pages()
    
    def on_tab_changed(self, index):
        """Handle tab change"""
        tab_names = ["Dashboard", "Reports", "Settings"]
        if 0 <= index < len(tab_names):
            self.status_bar.showMessage(f"Viewing {tab_names[index]}")
    
    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(self, "About RingCentral Reports",
                         "RingCentral Reports Application v1.0\n\n"
                         "A comprehensive reporting tool for RingCentral EX and CX data.\n"
                         "Load CSV reports, create custom views, and analyze your data.\n\n"
                         "By: William Weicker")
    
    def closeEvent(self, event):
        """Handle application close event"""
        reply = QMessageBox.question(self, 'Exit Application',
                                   'Are you sure you want to exit?',
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                                   QMessageBox.StandardButton.No)
        
        if reply == QMessageBox.StandardButton.Yes:
            event.accept()
        else:
            event.ignore()
