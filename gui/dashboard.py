"""
Dashboard Page for RingCentral Reports Application
Displays overview and summary information
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QTableWidget, QTableWidgetItem, QGroupBox,
                            QGridLayout, QFrame, QScrollArea)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont
import pandas as pd


class DashboardPage(QWidget):
    """Dashboard page showing data overview and key metrics"""
    
    def __init__(self, data_manager):
        super().__init__()
        self.data_manager = data_manager
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the dashboard UI"""
        layout = QVBoxLayout(self)
        
        # Title
        title_label = QLabel("📊 Dashboard Overview")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # Create scroll area for dashboard content
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        
        # Summary cards section
        self.create_summary_cards(scroll_layout)
        
        # Data overview section
        self.create_data_overview(scroll_layout)
        
        # Recent activity section
        self.create_recent_activity(scroll_layout)
        
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)
    
    def create_summary_cards(self, layout):
        """Create summary information cards"""
        cards_group = QGroupBox("Summary")
        cards_layout = QGridLayout(cards_group)
        
        # Create summary cards
        self.total_files_card = self.create_summary_card("Total Files", "0", "#0078d4")
        self.total_records_card = self.create_summary_card("Total Records", "0", "#107c10")
        self.unique_users_card = self.create_summary_card("Unique Users", "0", "#d83b01")
        self.date_range_card = self.create_summary_card("Date Range", "No data", "#5c2d91")
        
        # Add cards to grid
        cards_layout.addWidget(self.total_files_card, 0, 0)
        cards_layout.addWidget(self.total_records_card, 0, 1)
        cards_layout.addWidget(self.unique_users_card, 1, 0)
        cards_layout.addWidget(self.date_range_card, 1, 1)
        
        layout.addWidget(cards_group)
    
    def create_summary_card(self, title, value, color):
        """Create a summary card widget"""
        card = QFrame()
        card.setFrameStyle(QFrame.Shape.Box)
        card.setStyleSheet(f"""
            QFrame {{
                border: 2px solid {color};
                border-radius: 8px;
                background-color: #353535;
                padding: 10px;
            }}
        """)
        
        layout = QVBoxLayout(card)
        
        # Title
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet(f"color: {color}; font-weight: bold;")
        
        # Value
        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        value_font = QFont()
        value_font.setPointSize(18)
        value_font.setBold(True)
        value_label.setFont(value_font)
        value_label.setStyleSheet("color: white;")
        
        layout.addWidget(title_label)
        layout.addWidget(value_label)
        
        # Store value label for updates
        card.value_label = value_label
        
        return card
    
    def create_data_overview(self, layout):
        """Create data overview section"""
        overview_group = QGroupBox("Data Files Overview")
        overview_layout = QVBoxLayout(overview_group)
        
        # Create table for file information
        self.files_table = QTableWidget()
        self.files_table.setColumnCount(5)
        self.files_table.setHorizontalHeaderLabels(["Data Source", "Type", "Rows", "Columns", "Size"])
        self.files_table.horizontalHeader().setStretchLastSection(True)
        self.files_table.setAlternatingRowColors(True)
        self.files_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        
        overview_layout.addWidget(self.files_table)
        layout.addWidget(overview_group)
    
    def create_recent_activity(self, layout):
        """Create recent activity section"""
        activity_group = QGroupBox("Data Preview")
        activity_layout = QVBoxLayout(activity_group)
        
        # Create table for data preview
        self.preview_table = QTableWidget()
        self.preview_table.setAlternatingRowColors(True)
        self.preview_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        
        activity_layout.addWidget(self.preview_table)
        layout.addWidget(activity_group)
    
    def refresh_data(self):
        """Refresh dashboard with current data"""
        summary = self.data_manager.get_data_summary()
        
        # Update summary cards
        self.total_files_card.value_label.setText(str(summary['total_files']))
        self.total_records_card.value_label.setText(f"{summary['total_rows']:,}")
        self.unique_users_card.value_label.setText(str(summary['unique_users']))
        self.date_range_card.value_label.setText(summary['date_range'] or "No dates found")
        
        # Update files table
        self.update_files_table(summary)
        
        # Update data preview
        self.update_data_preview()
    
    def update_files_table(self, summary):
        """Update the files overview table"""
        file_names = summary['file_names']
        self.files_table.setRowCount(len(file_names))

        for row, data_source_name in enumerate(file_names):
            df = self.data_manager.get_file_data(data_source_name)
            if df is not None:
                # Determine if this is a sheet from an Excel file
                if '_' in data_source_name and not data_source_name.endswith('.csv'):
                    # This might be a sheet from an Excel file
                    parts = data_source_name.split('_')
                    if len(parts) >= 2:
                        file_part = '_'.join(parts[:-1])
                        sheet_part = parts[-1]
                        display_name = f"{file_part}.xlsx → {sheet_part}"
                        data_type = "XLSX Sheet"
                    else:
                        display_name = data_source_name
                        data_type = "Unknown"
                else:
                    # This is likely a CSV file or single-sheet Excel
                    display_name = data_source_name
                    if any(data_source_name.endswith(ext) for ext in ['.csv']):
                        data_type = "CSV File"
                    else:
                        data_type = "Data Source"

                # Data source name
                self.files_table.setItem(row, 0, QTableWidgetItem(display_name))

                # Type
                self.files_table.setItem(row, 1, QTableWidgetItem(data_type))

                # Rows
                self.files_table.setItem(row, 2, QTableWidgetItem(f"{len(df):,}"))

                # Columns
                self.files_table.setItem(row, 3, QTableWidgetItem(str(len(df.columns))))

                # Approximate size
                size_mb = df.memory_usage(deep=True).sum() / (1024 * 1024)
                self.files_table.setItem(row, 4, QTableWidgetItem(f"{size_mb:.1f} MB"))
    
    def update_data_preview(self):
        """Update the data preview table"""
        combined_data = self.data_manager.get_combined_data()
        
        if combined_data.empty:
            self.preview_table.setRowCount(0)
            self.preview_table.setColumnCount(0)
            return
        
        # Show first 10 rows and limit columns if too many
        preview_data = combined_data.head(10)
        max_columns = 8  # Limit columns for better display
        
        if len(preview_data.columns) > max_columns:
            preview_data = preview_data.iloc[:, :max_columns]
        
        # Set up table
        self.preview_table.setRowCount(len(preview_data))
        self.preview_table.setColumnCount(len(preview_data.columns))
        self.preview_table.setHorizontalHeaderLabels(list(preview_data.columns))
        
        # Populate table
        for row in range(len(preview_data)):
            for col in range(len(preview_data.columns)):
                value = preview_data.iloc[row, col]
                # Convert to string and limit length
                str_value = str(value)
                if len(str_value) > 50:
                    str_value = str_value[:47] + "..."
                
                item = QTableWidgetItem(str_value)
                self.preview_table.setItem(row, col, item)
        
        # Resize columns to content
        self.preview_table.resizeColumnsToContents()
