"""
Splash Screen for RingCentral Reports Application
Handles initial setup and folder creation
"""

import os
from PyQt6.QtWidgets import (QSplashScreen, QLabel, QVBoxLayout, 
                            QProgressBar, QWidget)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt6.QtGui import QPixmap, QPainter, QFont
from utils.file_utils import FileManager


class InitializationWorker(QThread):
    """Worker thread for initialization tasks"""
    progress_updated = pyqtSignal(int, str)
    initialization_complete = pyqtSignal(bool)
    
    def __init__(self):
        super().__init__()
        self.file_manager = FileManager()
    
    def run(self):
        """Run initialization tasks"""
        try:
            # Step 1: Check/Create RC Reports folder
            self.progress_updated.emit(20, "Checking RC Reports folder...")
            self.msleep(500)  # Simulate work
            
            folder_created = self.file_manager.ensure_rc_reports_folder()
            if not folder_created:
                self.initialization_complete.emit(False)
                return
            
            # Step 2: Scan for existing CSV files
            self.progress_updated.emit(50, "Scanning for CSV files...")
            self.msleep(500)
            
            csv_files = self.file_manager.get_csv_files()
            file_count = len(csv_files)
            
            # Step 3: Load application settings
            self.progress_updated.emit(80, "Loading application settings...")
            self.msleep(500)
            
            # Step 4: Complete initialization
            self.progress_updated.emit(100, f"Ready! Found {file_count} CSV files")
            self.msleep(300)
            
            self.initialization_complete.emit(True)
            
        except Exception as e:
            print(f"Initialization error: {e}")
            self.initialization_complete.emit(False)


class SplashScreen(QSplashScreen):
    """Custom splash screen with progress bar and status updates"""
    initialization_complete = pyqtSignal()
    
    def __init__(self):
        # Create a simple pixmap for the splash screen
        pixmap = QPixmap(400, 300)
        pixmap.fill(Qt.GlobalColor.darkGray)
        
        # Draw application title on pixmap
        painter = QPainter(pixmap)
        painter.setPen(Qt.GlobalColor.white)
        font = QFont("Arial", 16, QFont.Weight.Bold)
        painter.setFont(font)
        painter.drawText(pixmap.rect(), Qt.AlignmentFlag.AlignCenter, 
                        "RingCentral Reports\nApplication")
        painter.end()
        
        super().__init__(pixmap)
        
        # Set up the splash screen layout
        self.setup_ui()
        
        # Initialize worker thread
        self.worker = InitializationWorker()
        self.worker.progress_updated.connect(self.update_progress)
        self.worker.initialization_complete.connect(self.on_initialization_complete)
    
    def setup_ui(self):
        """Set up the splash screen UI elements"""
        # Create a widget to hold our custom elements
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid grey;
                border-radius: 5px;
                text-align: center;
                background-color: #404040;
                color: white;
            }
            QProgressBar::chunk {
                background-color: #0078d4;
                border-radius: 3px;
            }
        """)
        
        # Status label
        self.status_label = QLabel("Initializing...")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.status_label.setStyleSheet("color: white; font-size: 12px;")
        
        # Add widgets to layout
        layout.addStretch()
        layout.addWidget(self.progress_bar)
        layout.addWidget(self.status_label)
        layout.setContentsMargins(20, 0, 20, 20)
        
        # Set the widget as the splash screen's widget
        widget.setGeometry(0, 200, 400, 100)
        widget.setParent(self)
    
    def start_initialization(self):
        """Start the initialization process"""
        self.worker.start()
    
    def update_progress(self, value: int, message: str):
        """Update progress bar and status message"""
        self.progress_bar.setValue(value)
        self.status_label.setText(message)
        self.showMessage(message, Qt.AlignmentFlag.AlignBottom | Qt.AlignmentFlag.AlignCenter, 
                        Qt.GlobalColor.white)
    
    def on_initialization_complete(self, success: bool):
        """Handle initialization completion"""
        if success:
            # Wait a moment before closing
            QTimer.singleShot(1000, self.initialization_complete.emit)
        else:
            self.status_label.setText("Initialization failed!")
            # Still emit signal to show main window (with error handling there)
            QTimer.singleShot(2000, self.initialization_complete.emit)
