"""
Reports Page for RingCentral Reports Application
Handles report creation, viewing, filtering, and exporting
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QTableWidget, QTableWidgetItem, QPushButton,
                            QComboBox, QLineEdit, QGroupBox, QSplitter,
                            QListWidget, QMessageBox, QFileDialog,
                            QCheckBox, QScrollArea, QRadioButton)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont
import pandas as pd
from datetime import datetime
from core.report_engine import ReportEngine


class ReportsPage(QWidget):
    """Reports page for creating and managing custom reports"""
    
    def __init__(self, data_manager):
        super().__init__()
        self.data_manager = data_manager
        self.report_engine = ReportEngine(data_manager)
        self.current_filtered_data = pd.DataFrame()
        self.saved_reports = {}  # Will store saved report configurations
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the reports page UI"""
        layout = QVBoxLayout(self)
        
        # Title
        title_label = QLabel("📋 Reports & Analytics")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # Create main splitter
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        layout.addWidget(main_splitter)
        
        # Left panel - Report controls
        self.create_control_panel(main_splitter)
        
        # Right panel - Data display
        self.create_data_panel(main_splitter)
        
        # Set splitter proportions
        main_splitter.setSizes([300, 900])
    
    def create_control_panel(self, parent):
        """Create the left control panel"""
        control_widget = QWidget()
        control_layout = QVBoxLayout(control_widget)
        
        # Saved Reports section
        reports_group = QGroupBox("Saved Reports")
        reports_layout = QVBoxLayout(reports_group)
        
        self.reports_list = QListWidget()
        self.reports_list.itemClicked.connect(self.load_saved_report)
        reports_layout.addWidget(self.reports_list)
        
        # Report management buttons
        reports_buttons_layout = QHBoxLayout()
        self.new_report_btn = QPushButton("New Report")
        self.new_report_btn.clicked.connect(self.create_new_report)
        self.save_report_btn = QPushButton("Save Current")
        self.save_report_btn.clicked.connect(self.save_current_report)
        self.delete_report_btn = QPushButton("Delete")
        self.delete_report_btn.clicked.connect(self.delete_saved_report)
        
        reports_buttons_layout.addWidget(self.new_report_btn)
        reports_buttons_layout.addWidget(self.save_report_btn)
        reports_buttons_layout.addWidget(self.delete_report_btn)
        reports_layout.addLayout(reports_buttons_layout)
        
        control_layout.addWidget(reports_group)

        # Filters section
        filters_group = QGroupBox("Filters")
        filters_layout = QVBoxLayout(filters_group)
        
        # Column filter
        filters_layout.addWidget(QLabel("Filter by Column:"))
        self.column_combo = QComboBox()
        self.column_combo.currentTextChanged.connect(self.on_column_changed)
        filters_layout.addWidget(self.column_combo)
        
        # Value filter
        filters_layout.addWidget(QLabel("Filter Value:"))
        self.filter_value_input = QLineEdit()
        self.filter_value_input.setPlaceholderText("Enter filter value...")
        filters_layout.addWidget(self.filter_value_input)
        
        # User group filter
        filters_layout.addWidget(QLabel("User Group:"))
        self.user_group_combo = QComboBox()
        self.user_group_combo.addItem("All Users")
        filters_layout.addWidget(self.user_group_combo)
        
        # Date range (placeholder for future implementation)
        filters_layout.addWidget(QLabel("Date Range:"))
        date_layout = QHBoxLayout()
        self.start_date_input = QLineEdit()
        self.start_date_input.setPlaceholderText("Start date...")
        self.end_date_input = QLineEdit()
        self.end_date_input.setPlaceholderText("End date...")
        date_layout.addWidget(self.start_date_input)
        date_layout.addWidget(self.end_date_input)
        filters_layout.addLayout(date_layout)
        
        # Apply filters button
        self.apply_filters_btn = QPushButton("Apply Filters")
        self.apply_filters_btn.clicked.connect(self.apply_filters)
        filters_layout.addWidget(self.apply_filters_btn)
        
        # Clear filters button
        self.clear_filters_btn = QPushButton("Clear Filters")
        self.clear_filters_btn.clicked.connect(self.clear_filters)
        filters_layout.addWidget(self.clear_filters_btn)
        
        control_layout.addWidget(filters_group)
        
        # Column selection section
        columns_group = QGroupBox("Visible Columns")
        columns_layout = QVBoxLayout(columns_group)
        
        # Scroll area for column checkboxes
        columns_scroll = QScrollArea()
        self.columns_widget = QWidget()
        self.columns_layout = QVBoxLayout(self.columns_widget)
        columns_scroll.setWidget(self.columns_widget)
        columns_scroll.setWidgetResizable(True)
        columns_scroll.setMaximumHeight(200)
        columns_layout.addWidget(columns_scroll)
        
        # Select all/none buttons
        column_buttons_layout = QHBoxLayout()
        self.select_all_btn = QPushButton("Select All")
        self.select_all_btn.clicked.connect(self.select_all_columns)
        self.select_none_btn = QPushButton("Select None")
        self.select_none_btn.clicked.connect(self.select_no_columns)
        column_buttons_layout.addWidget(self.select_all_btn)
        column_buttons_layout.addWidget(self.select_none_btn)
        columns_layout.addLayout(column_buttons_layout)
        
        control_layout.addWidget(columns_group)
        
        # Export section
        export_group = QGroupBox("Export")
        export_layout = QVBoxLayout(export_group)
        
        self.export_csv_btn = QPushButton("Export to CSV")
        self.export_csv_btn.clicked.connect(self.export_to_csv)
        export_layout.addWidget(self.export_csv_btn)
        
        control_layout.addWidget(export_group)
        
        # Add stretch to push everything to top
        control_layout.addStretch()
        
        parent.addWidget(control_widget)
    
    def create_data_panel(self, parent):
        """Create the right data display panel"""
        data_widget = QWidget()
        data_layout = QVBoxLayout(data_widget)
        
        # Info bar
        info_layout = QHBoxLayout()
        self.info_label = QLabel("User Consolidated Report - No data loaded")
        self.refresh_btn = QPushButton("Refresh Report")
        self.refresh_btn.clicked.connect(self.refresh_data)
        
        info_layout.addWidget(self.info_label)
        info_layout.addStretch()
        info_layout.addWidget(self.refresh_btn)
        data_layout.addLayout(info_layout)
        
        # Data table
        self.data_table = QTableWidget()
        self.data_table.setAlternatingRowColors(True)
        self.data_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.data_table.setSortingEnabled(True)
        data_layout.addWidget(self.data_table)
        
        parent.addWidget(data_widget)
    
    def refresh_data(self):
        """Refresh the reports page with current data"""
        print("Reports: Refreshing data...")

        # Refresh the report engine with latest data
        self.report_engine = ReportEngine(self.data_manager)

        # Load user consolidated data first
        self.load_user_consolidated_data()

        # Then update UI components based on the loaded data
        self.update_column_combo()
        self.update_user_groups_combo()
        self.update_column_checkboxes()
    
    def update_column_combo(self):
        """Update the column filter combo box"""
        self.column_combo.clear()
        self.column_combo.addItem("Select column...")

        # Use columns from the user consolidated report instead of raw data
        if not self.current_filtered_data.empty:
            columns = list(self.current_filtered_data.columns)
        else:
            # Fallback to raw data columns if no user report is available yet
            columns = self.data_manager.get_available_columns()

        for column in columns:
            self.column_combo.addItem(column)
    
    def update_user_groups_combo(self):
        """Update the user groups combo box"""
        self.user_group_combo.clear()
        self.user_group_combo.addItem("All Users")
        
        user_groups = self.data_manager.get_user_groups()
        for group_name in user_groups.keys():
            self.user_group_combo.addItem(group_name)
    
    def update_column_checkboxes(self):
        """Update the column visibility checkboxes"""
        # Clear existing checkboxes
        for i in reversed(range(self.columns_layout.count())):
            child = self.columns_layout.itemAt(i)
            if child and child.widget():
                child.widget().setParent(None)

        # Add checkboxes for each column from user consolidated report
        if not self.current_filtered_data.empty:
            columns = list(self.current_filtered_data.columns)
        else:
            # Fallback to raw data columns if no user report is available yet
            columns = self.data_manager.get_available_columns()

        self.column_checkboxes = {}

        for column in columns:
            checkbox = QCheckBox(column)
            checkbox.setChecked(True)  # All columns visible by default
            checkbox.stateChanged.connect(self.update_table_columns)
            self.column_checkboxes[column] = checkbox
            self.columns_layout.addWidget(checkbox)
    
    def load_user_consolidated_data(self):
        """Load user consolidated report data - the default and primary view"""
        try:
            print("Loading user consolidated data...")

            # Check if we have any data in the data manager
            combined_data = self.data_manager.get_combined_data()
            print(f"Combined data shape: {combined_data.shape}")

            if combined_data.empty:
                print("No combined data available")
                self.current_filtered_data = pd.DataFrame()
                self.update_data_table()
                return

            # Generate user consolidated report
            user_report_df = self.report_engine.generate_user_consolidated_report()
            print(f"User report shape: {user_report_df.shape}")
            print(f"User report columns: {list(user_report_df.columns) if not user_report_df.empty else 'No columns'}")

            self.current_filtered_data = user_report_df.copy()
            self.update_data_table()

        except Exception as e:
            print(f"Error loading user consolidated data: {e}")
            import traceback
            traceback.print_exc()
            self.current_filtered_data = pd.DataFrame()
            self.update_data_table()
    
    def update_data_table(self):
        """Update the data table with current filtered data"""
        if self.current_filtered_data.empty:
            self.data_table.setRowCount(0)
            self.data_table.setColumnCount(0)
            self.info_label.setText("No data to display")
            return
        
        # Get visible columns
        visible_columns = [col for col, checkbox in self.column_checkboxes.items() 
                          if checkbox.isChecked()]
        
        if not visible_columns:
            self.data_table.setRowCount(0)
            self.data_table.setColumnCount(0)
            self.info_label.setText("No columns selected")
            return
        
        # Filter data to visible columns
        display_data = self.current_filtered_data[visible_columns]
        
        # Set up table
        self.data_table.setRowCount(len(display_data))
        self.data_table.setColumnCount(len(visible_columns))
        self.data_table.setHorizontalHeaderLabels(visible_columns)
        
        # Populate table
        for row in range(len(display_data)):
            for col, column_name in enumerate(visible_columns):
                value = display_data.iloc[row][column_name]
                str_value = str(value) if pd.notna(value) else ""
                
                item = QTableWidgetItem(str_value)
                self.data_table.setItem(row, col, item)
        
        # Update info label
        total_rows = len(self.current_filtered_data)
        visible_cols = len(visible_columns)
        self.info_label.setText(f"User Consolidated Report - Showing {total_rows:,} users, {visible_cols} columns")
        
        # Resize columns
        self.data_table.resizeColumnsToContents()
    
    def apply_filters(self):
        """Apply current filters to the user consolidated data"""
        # Start with fresh user consolidated data
        try:
            user_report_df = self.report_engine.generate_user_consolidated_report()

            if user_report_df.empty:
                self.current_filtered_data = pd.DataFrame()
                self.update_data_table()
                return

            filtered_data = user_report_df.copy()

            # Apply column filter
            column = self.column_combo.currentText()
            filter_value = self.filter_value_input.text().strip()

            if column != "Select column..." and filter_value and column in filtered_data.columns:
                # Apply string contains filter (case insensitive)
                filtered_data = filtered_data[
                    filtered_data[column].astype(str).str.contains(filter_value, case=False, na=False)
                ]

            # Apply user group filter
            user_group = self.user_group_combo.currentText()
            if user_group != "All Users":
                user_groups = self.data_manager.get_user_groups()
                if user_group in user_groups:
                    group_users = user_groups[user_group]
                    # Filter by User Name column (first column in user consolidated report)
                    if 'User Name' in filtered_data.columns:
                        filtered_data = filtered_data[filtered_data['User Name'].isin(group_users)]

            self.current_filtered_data = filtered_data
            self.update_data_table()

        except Exception as e:
            print(f"Error applying filters: {e}")
            self.current_filtered_data = pd.DataFrame()
            self.update_data_table()
    
    def clear_filters(self):
        """Clear all filters and show all user data"""
        self.filter_value_input.clear()
        self.column_combo.setCurrentIndex(0)
        self.user_group_combo.setCurrentIndex(0)
        self.start_date_input.clear()
        self.end_date_input.clear()
        self.load_user_consolidated_data()
    
    def on_column_changed(self):
        """Handle column selection change"""
        # Could add logic here to show sample values for the selected column
        pass
    
    def update_table_columns(self):
        """Update table when column visibility changes"""
        self.update_data_table()
    
    def select_all_columns(self):
        """Select all column checkboxes"""
        for checkbox in self.column_checkboxes.values():
            checkbox.setChecked(True)
    
    def select_no_columns(self):
        """Deselect all column checkboxes"""
        for checkbox in self.column_checkboxes.values():
            checkbox.setChecked(False)
    
    def export_to_csv(self):
        """Export current filtered data to CSV"""
        if self.current_filtered_data.empty:
            QMessageBox.warning(self, "No Data", "No data to export.")
            return
        
        # Get visible columns
        visible_columns = [col for col, checkbox in self.column_checkboxes.items() 
                          if checkbox.isChecked()]
        
        if not visible_columns:
            QMessageBox.warning(self, "No Columns", "No columns selected for export.")
            return
        
        # Get file path
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        default_filename = f"rc_report_{timestamp}.csv"
        
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Export Report", default_filename, "CSV Files (*.csv)")
        
        if file_path:
            try:
                export_data = self.current_filtered_data[visible_columns]
                export_data.to_csv(file_path, index=False)
                QMessageBox.information(self, "Export Successful", 
                                      f"Report exported to:\n{file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Export Error", f"Error exporting file:\n{str(e)}")
    
    def create_new_report(self):
        """Create a new report (clear current settings and load fresh user data)"""
        self.clear_filters()
        self.select_all_columns()
        # Refresh the report engine and load fresh user data
        self.report_engine = ReportEngine(self.data_manager)
        self.load_user_consolidated_data()
    
    def save_current_report(self):
        """Save current report configuration"""
        # This would implement saving report settings
        # For now, just show a placeholder message
        QMessageBox.information(self, "Save Report", 
                              "Report saving functionality will be implemented in a future version.")
    
    def load_saved_report(self, item):
        """Load a saved report configuration"""
        # This would implement loading saved report settings
        pass
    
    def delete_saved_report(self):
        """Delete selected saved report"""
        # This would implement deleting saved reports
        pass
