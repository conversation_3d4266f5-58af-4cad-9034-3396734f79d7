"""
Settings Page for RingCentral Reports Application
Handles user groups, report configurations, and application settings
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QListWidget, QPushButton, QLineEdit, QGroupBox,
                            QSplitter, QMessageBox, QInputDialog, QTextEdit,
                            QTabWidget, QFileDialog, QCheckBox, QSpinBox)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont
import json
import os


class SettingsPage(QWidget):
    """Settings page for managing user groups and application configuration"""
    
    data_refreshed = pyqtSignal()
    
    def __init__(self, data_manager):
        super().__init__()
        self.data_manager = data_manager
        self.setup_ui()
        self.load_settings()
    
    def setup_ui(self):
        """Set up the settings page UI"""
        layout = QVBoxLayout(self)
        
        # Title
        title_label = QLabel("⚙️ Settings & Configuration")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # Create tab widget for different settings sections
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # User Groups tab
        self.create_user_groups_tab()
        
        # Data Management tab
        self.create_data_management_tab()
        
        # Application Settings tab
        self.create_app_settings_tab()
    
    def create_user_groups_tab(self):
        """Create the user groups management tab"""
        groups_widget = QWidget()
        layout = QHBoxLayout(groups_widget)
        
        # Left panel - Groups list
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        
        groups_group = QGroupBox("User Groups")
        groups_layout = QVBoxLayout(groups_group)
        
        self.groups_list = QListWidget()
        self.groups_list.itemClicked.connect(self.on_group_selected)
        groups_layout.addWidget(self.groups_list)
        
        # Group management buttons
        group_buttons_layout = QHBoxLayout()
        self.new_group_btn = QPushButton("New Group")
        self.new_group_btn.clicked.connect(self.create_new_group)
        self.edit_group_btn = QPushButton("Edit Group")
        self.edit_group_btn.clicked.connect(self.edit_selected_group)
        self.delete_group_btn = QPushButton("Delete Group")
        self.delete_group_btn.clicked.connect(self.delete_selected_group)
        
        group_buttons_layout.addWidget(self.new_group_btn)
        group_buttons_layout.addWidget(self.edit_group_btn)
        group_buttons_layout.addWidget(self.delete_group_btn)
        groups_layout.addLayout(group_buttons_layout)
        
        left_layout.addWidget(groups_group)
        left_layout.addStretch()
        
        # Right panel - Group details
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        
        # Group name
        name_group = QGroupBox("Group Details")
        name_layout = QVBoxLayout(name_group)
        
        name_layout.addWidget(QLabel("Group Name:"))
        self.group_name_input = QLineEdit()
        self.group_name_input.setPlaceholderText("Enter group name...")
        name_layout.addWidget(self.group_name_input)
        
        right_layout.addWidget(name_group)
        
        # Available users
        users_group = QGroupBox("Available Users")
        users_layout = QVBoxLayout(users_group)
        
        # Search box
        self.user_search_input = QLineEdit()
        self.user_search_input.setPlaceholderText("Search users...")
        self.user_search_input.textChanged.connect(self.filter_users)
        users_layout.addWidget(self.user_search_input)
        
        # Users list
        self.available_users_list = QListWidget()
        self.available_users_list.setSelectionMode(QListWidget.SelectionMode.MultiSelection)
        users_layout.addWidget(self.available_users_list)
        
        # Add/Remove buttons
        user_buttons_layout = QHBoxLayout()
        self.add_users_btn = QPushButton("Add Selected →")
        self.add_users_btn.clicked.connect(self.add_users_to_group)
        self.remove_users_btn = QPushButton("← Remove Selected")
        self.remove_users_btn.clicked.connect(self.remove_users_from_group)
        
        user_buttons_layout.addWidget(self.add_users_btn)
        user_buttons_layout.addWidget(self.remove_users_btn)
        users_layout.addLayout(user_buttons_layout)
        
        right_layout.addWidget(users_group)
        
        # Group members
        members_group = QGroupBox("Group Members")
        members_layout = QVBoxLayout(members_group)
        
        self.group_members_list = QListWidget()
        self.group_members_list.setSelectionMode(QListWidget.SelectionMode.MultiSelection)
        members_layout.addWidget(self.group_members_list)
        
        right_layout.addWidget(members_group)
        
        # Save group button
        self.save_group_btn = QPushButton("Save Group")
        self.save_group_btn.clicked.connect(self.save_current_group)
        right_layout.addWidget(self.save_group_btn)
        
        # Add panels to splitter
        splitter = QSplitter(Qt.Orientation.Horizontal)
        splitter.addWidget(left_panel)
        splitter.addWidget(right_panel)
        splitter.setSizes([300, 500])
        
        layout.addWidget(splitter)
        self.tab_widget.addTab(groups_widget, "User Groups")
    
    def create_data_management_tab(self):
        """Create the data management tab"""
        data_widget = QWidget()
        layout = QVBoxLayout(data_widget)
        
        # Data folder info
        folder_group = QGroupBox("Data Folder")
        folder_layout = QVBoxLayout(folder_group)
        
        self.folder_path_label = QLabel()
        folder_layout.addWidget(QLabel("RC Reports Folder:"))
        folder_layout.addWidget(self.folder_path_label)
        
        folder_buttons_layout = QHBoxLayout()
        self.open_folder_btn = QPushButton("Open Folder")
        self.open_folder_btn.clicked.connect(self.open_data_folder)
        self.refresh_data_btn = QPushButton("Refresh Data")
        self.refresh_data_btn.clicked.connect(self.refresh_all_data)
        
        folder_buttons_layout.addWidget(self.open_folder_btn)
        folder_buttons_layout.addWidget(self.refresh_data_btn)
        folder_layout.addLayout(folder_buttons_layout)
        
        layout.addWidget(folder_group)
        
        # Data files info
        files_group = QGroupBox("Data Files")
        files_layout = QVBoxLayout(files_group)
        
        self.files_info_text = QTextEdit()
        self.files_info_text.setReadOnly(True)
        self.files_info_text.setMaximumHeight(200)
        files_layout.addWidget(self.files_info_text)
        
        layout.addWidget(files_group)
        
        # Data processing options
        processing_group = QGroupBox("Data Processing Options")
        processing_layout = QVBoxLayout(processing_group)
        
        self.auto_refresh_checkbox = QCheckBox("Auto-refresh data on startup")
        self.auto_refresh_checkbox.setChecked(True)
        processing_layout.addWidget(self.auto_refresh_checkbox)
        
        self.combine_files_checkbox = QCheckBox("Automatically combine all CSV files")
        self.combine_files_checkbox.setChecked(True)
        processing_layout.addWidget(self.combine_files_checkbox)
        
        layout.addWidget(processing_group)
        
        layout.addStretch()
        self.tab_widget.addTab(data_widget, "Data Management")
    
    def create_app_settings_tab(self):
        """Create the application settings tab"""
        app_widget = QWidget()
        layout = QVBoxLayout(app_widget)
        
        # Display settings
        display_group = QGroupBox("Display Settings")
        display_layout = QVBoxLayout(display_group)
        
        display_layout.addWidget(QLabel("Default rows to display:"))
        self.default_rows_spinbox = QSpinBox()
        self.default_rows_spinbox.setRange(10, 10000)
        self.default_rows_spinbox.setValue(1000)
        display_layout.addWidget(self.default_rows_spinbox)
        
        self.dark_theme_checkbox = QCheckBox("Use dark theme")
        self.dark_theme_checkbox.setChecked(True)
        display_layout.addWidget(self.dark_theme_checkbox)
        
        layout.addWidget(display_group)
        
        # Export settings
        export_group = QGroupBox("Export Settings")
        export_layout = QVBoxLayout(export_group)
        
        export_layout.addWidget(QLabel("Default export format:"))
        self.export_format_combo = QLineEdit("CSV")
        self.export_format_combo.setReadOnly(True)
        export_layout.addWidget(self.export_format_combo)
        
        self.include_index_checkbox = QCheckBox("Include row index in exports")
        export_layout.addWidget(self.include_index_checkbox)
        
        layout.addWidget(export_group)
        
        # Save settings button
        self.save_settings_btn = QPushButton("Save Settings")
        self.save_settings_btn.clicked.connect(self.save_settings)
        layout.addWidget(self.save_settings_btn)
        
        layout.addStretch()
        self.tab_widget.addTab(app_widget, "Application")
    
    def refresh_data(self):
        """Refresh settings page with current data"""
        self.update_available_users()
        self.update_groups_list()
        self.update_data_info()
    
    def update_available_users(self):
        """Update the available users list"""
        self.available_users_list.clear()
        users = self.data_manager.get_unique_users()
        for user in users:
            self.available_users_list.addItem(user)
    
    def update_groups_list(self):
        """Update the groups list"""
        self.groups_list.clear()
        groups = self.data_manager.get_user_groups()
        for group_name in groups.keys():
            self.groups_list.addItem(group_name)
    
    def update_data_info(self):
        """Update data folder and files information"""
        folder_path = self.data_manager.file_manager.get_rc_reports_path()
        self.folder_path_label.setText(str(folder_path))
        
        # Update files info
        summary = self.data_manager.get_data_summary()
        info_text = f"Total Files: {summary['total_files']}\n"
        info_text += f"Total Records: {summary['total_rows']:,}\n"
        info_text += f"Unique Users: {summary['unique_users']}\n"
        if summary['date_range']:
            info_text += f"Date Range: {summary['date_range']}\n"
        
        info_text += "\nFiles:\n"
        for filename in summary['file_names']:
            df = self.data_manager.get_file_data(filename)
            if df is not None:
                info_text += f"  • {filename}: {len(df):,} rows\n"
        
        self.files_info_text.setPlainText(info_text)
    
    def filter_users(self):
        """Filter available users based on search text"""
        search_text = self.user_search_input.text().lower()
        for i in range(self.available_users_list.count()):
            item = self.available_users_list.item(i)
            item.setHidden(search_text not in item.text().lower())
    
    def create_new_group(self):
        """Create a new user group"""
        self.group_name_input.clear()
        self.group_members_list.clear()
        self.groups_list.clearSelection()
    
    def on_group_selected(self, item):
        """Handle group selection"""
        group_name = item.text()
        groups = self.data_manager.get_user_groups()
        
        if group_name in groups:
            self.group_name_input.setText(group_name)
            
            # Update group members list
            self.group_members_list.clear()
            for user in groups[group_name]:
                self.group_members_list.addItem(user)
    
    def add_users_to_group(self):
        """Add selected users to the current group"""
        selected_items = self.available_users_list.selectedItems()
        for item in selected_items:
            user = item.text()
            # Check if user is already in group
            existing_users = [self.group_members_list.item(i).text() 
                            for i in range(self.group_members_list.count())]
            if user not in existing_users:
                self.group_members_list.addItem(user)
    
    def remove_users_from_group(self):
        """Remove selected users from the current group"""
        selected_items = self.group_members_list.selectedItems()
        for item in selected_items:
            row = self.group_members_list.row(item)
            self.group_members_list.takeItem(row)
    
    def save_current_group(self):
        """Save the current group configuration"""
        group_name = self.group_name_input.text().strip()
        
        if not group_name:
            QMessageBox.warning(self, "Invalid Group", "Please enter a group name.")
            return
        
        # Get group members
        members = [self.group_members_list.item(i).text() 
                  for i in range(self.group_members_list.count())]
        
        if not members:
            QMessageBox.warning(self, "Empty Group", "Please add at least one user to the group.")
            return
        
        # Save group
        self.data_manager.create_user_group(group_name, members)
        self.update_groups_list()
        
        QMessageBox.information(self, "Group Saved", f"Group '{group_name}' saved successfully.")
    
    def edit_selected_group(self):
        """Edit the selected group"""
        current_item = self.groups_list.currentItem()
        if current_item:
            self.on_group_selected(current_item)
    
    def delete_selected_group(self):
        """Delete the selected group"""
        current_item = self.groups_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "No Selection", "Please select a group to delete.")
            return
        
        group_name = current_item.text()
        reply = QMessageBox.question(self, "Delete Group", 
                                   f"Are you sure you want to delete group '{group_name}'?",
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        
        if reply == QMessageBox.StandardButton.Yes:
            self.data_manager.delete_user_group(group_name)
            self.update_groups_list()
            self.group_name_input.clear()
            self.group_members_list.clear()
    
    def open_data_folder(self):
        """Open the RC Reports data folder"""
        folder_path = self.data_manager.file_manager.get_rc_reports_path()
        os.startfile(str(folder_path))  # Windows specific
    
    def refresh_all_data(self):
        """Refresh all data and emit signal"""
        success = self.data_manager.load_all_data()
        if success:
            self.data_refreshed.emit()
            self.refresh_data()
            QMessageBox.information(self, "Data Refreshed", "Data has been refreshed successfully.")
        else:
            QMessageBox.warning(self, "Refresh Failed", "No data files found to refresh.")
    
    def save_settings(self):
        """Save application settings"""
        # This would implement saving settings to a config file
        QMessageBox.information(self, "Settings Saved", "Settings have been saved successfully.")
    
    def load_settings(self):
        """Load application settings"""
        # This would implement loading settings from a config file
        pass
