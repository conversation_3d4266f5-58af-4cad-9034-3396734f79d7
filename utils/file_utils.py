"""
File utilities for RingCentral Reports application
Handles folder creation, CSV file operations, and file management
"""

import os
import csv
import pandas as pd
from pathlib import Path
from typing import List, Dict, Optional


class FileManager:
    """Manages file operations for the application"""
    
    def __init__(self):
        self.documents_path = Path.home() / "Documents"
        self.rc_reports_folder = self.documents_path / "RC Reports"
    
    def ensure_rc_reports_folder(self) -> bool:
        """
        Ensure the RC Reports folder exists in the user's Documents folder
        Returns True if folder exists or was created successfully
        """
        try:
            if not self.rc_reports_folder.exists():
                self.rc_reports_folder.mkdir(parents=True, exist_ok=True)
                print(f"Created RC Reports folder at: {self.rc_reports_folder}")
            else:
                print(f"RC Reports folder already exists at: {self.rc_reports_folder}")
            return True
        except Exception as e:
            print(f"Error creating RC Reports folder: {e}")
            return False
    
    def get_data_files(self) -> List[Path]:
        """
        Get all CSV and XLSX files in the RC Reports folder
        Returns list of Path objects for data files
        """
        if not self.rc_reports_folder.exists():
            return []

        csv_files = list(self.rc_reports_folder.glob("*.csv"))
        xlsx_files = list(self.rc_reports_folder.glob("*.xlsx"))
        return csv_files + xlsx_files

    def get_csv_files(self) -> List[Path]:
        """
        Get all CSV files in the RC Reports folder (for backward compatibility)
        Returns list of Path objects for CSV files
        """
        if not self.rc_reports_folder.exists():
            return []

        csv_files = list(self.rc_reports_folder.glob("*.csv"))
        return csv_files
    
    def load_data_file(self, file_path: Path) -> Optional[pd.DataFrame]:
        """
        Load a CSV or XLSX file into a pandas DataFrame
        Returns DataFrame or None if error
        """
        try:
            file_extension = file_path.suffix.lower()

            if file_extension == '.csv':
                df = pd.read_csv(file_path)
                print(f"Loaded CSV file: {file_path.name} with {len(df)} rows")
            elif file_extension == '.xlsx':
                df = pd.read_excel(file_path)
                print(f"Loaded XLSX file: {file_path.name} with {len(df)} rows")
            else:
                print(f"Unsupported file format: {file_extension}")
                return None

            return df
        except Exception as e:
            print(f"Error loading file {file_path}: {e}")
            return None

    def load_csv_file(self, file_path: Path) -> Optional[pd.DataFrame]:
        """
        Load a CSV file into a pandas DataFrame (for backward compatibility)
        Returns DataFrame or None if error
        """
        try:
            df = pd.read_csv(file_path)
            print(f"Loaded CSV file: {file_path.name} with {len(df)} rows")
            return df
        except Exception as e:
            print(f"Error loading CSV file {file_path}: {e}")
            return None
    
    def load_all_data_files(self) -> Dict[str, pd.DataFrame]:
        """
        Load all CSV and XLSX files in the RC Reports folder
        Returns dictionary with filename as key and DataFrame as value
        """
        data_files = self.get_data_files()
        data_frames = {}

        for file_path in data_files:
            df = self.load_data_file(file_path)
            if df is not None:
                data_frames[file_path.stem] = df

        return data_frames

    def load_all_csv_files(self) -> Dict[str, pd.DataFrame]:
        """
        Load all CSV files in the RC Reports folder (for backward compatibility)
        Returns dictionary with filename as key and DataFrame as value
        """
        csv_files = self.get_csv_files()
        data_frames = {}

        for file_path in csv_files:
            df = self.load_csv_file(file_path)
            if df is not None:
                data_frames[file_path.stem] = df

        return data_frames
    
    def save_csv_file(self, data: pd.DataFrame, filename: str) -> bool:
        """
        Save DataFrame to CSV file in RC Reports folder
        Returns True if successful
        """
        try:
            file_path = self.rc_reports_folder / f"{filename}.csv"
            data.to_csv(file_path, index=False)
            print(f"Saved CSV file: {file_path}")
            return True
        except Exception as e:
            print(f"Error saving CSV file: {e}")
            return False
    
    def get_rc_reports_path(self) -> Path:
        """Get the path to the RC Reports folder"""
        return self.rc_reports_folder
    
    def file_exists(self, filename: str) -> bool:
        """Check if a file exists in the RC Reports folder"""
        file_path = self.rc_reports_folder / filename
        return file_path.exists()
