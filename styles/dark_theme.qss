/* Dark Theme Stylesheet for RingCentral Reports Application */

QMainWindow {
    background-color: #2b2b2b;
    color: #ffffff;
}

QWidget {
    background-color: #2b2b2b;
    color: #ffffff;
    font-family: "Segoe UI", Arial, sans-serif;
    font-size: 10pt;
}

/* Buttons */
QPushButton {
    background-color: #404040;
    border: 1px solid #555555;
    border-radius: 4px;
    padding: 8px 16px;
    color: #ffffff;
    font-weight: bold;
}

QPushButton:hover {
    background-color: #505050;
    border-color: #777777;
}

QPushButton:pressed {
    background-color: #353535;
}

QPushButton:disabled {
    background-color: #2b2b2b;
    color: #666666;
    border-color: #444444;
}

/* Primary Button */
QPushButton[class="primary"] {
    background-color: #0078d4;
    border-color: #106ebe;
}

QPushButton[class="primary"]:hover {
    background-color: #106ebe;
}

/* Tab Widget */
QTabWidget::pane {
    border: 1px solid #555555;
    background-color: #2b2b2b;
}

QTabBar::tab {
    background-color: #404040;
    border: 1px solid #555555;
    padding: 8px 16px;
    margin-right: 2px;
}

QTabBar::tab:selected {
    background-color: #0078d4;
    border-bottom-color: #0078d4;
}

QTabBar::tab:hover {
    background-color: #505050;
}

/* Table Widget */
QTableWidget {
    background-color: #353535;
    alternate-background-color: #404040;
    gridline-color: #555555;
    selection-background-color: #0078d4;
}

QTableWidget::item {
    padding: 4px;
    border: none;
}

QHeaderView::section {
    background-color: #404040;
    border: 1px solid #555555;
    padding: 4px;
    font-weight: bold;
}

/* Scroll Bars */
QScrollBar:vertical {
    background-color: #404040;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background-color: #666666;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #777777;
}

/* Line Edit */
QLineEdit {
    background-color: #353535;
    border: 1px solid #555555;
    border-radius: 4px;
    padding: 4px;
}

QLineEdit:focus {
    border-color: #0078d4;
}

/* Combo Box */
QComboBox {
    background-color: #353535;
    border: 1px solid #555555;
    border-radius: 4px;
    padding: 4px;
}

QComboBox::drop-down {
    border: none;
}

QComboBox::down-arrow {
    image: url(down_arrow.png);
    width: 12px;
    height: 12px;
}

QComboBox QAbstractItemView {
    background-color: #353535;
    border: 1px solid #555555;
    selection-background-color: #0078d4;
}

/* Group Box */
QGroupBox {
    border: 1px solid #555555;
    border-radius: 4px;
    margin-top: 10px;
    padding-top: 10px;
    font-weight: bold;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 5px 0 5px;
}

/* List Widget */
QListWidget {
    background-color: #353535;
    border: 1px solid #555555;
    alternate-background-color: #404040;
}

QListWidget::item {
    padding: 4px;
    border-bottom: 1px solid #555555;
}

QListWidget::item:selected {
    background-color: #0078d4;
}

/* Splitter */
QSplitter::handle {
    background-color: #555555;
}

QSplitter::handle:horizontal {
    width: 2px;
}

QSplitter::handle:vertical {
    height: 2px;
}

/* Status Bar */
QStatusBar {
    background-color: #404040;
    border-top: 1px solid #555555;
}

/* Menu Bar */
QMenuBar {
    background-color: #404040;
    border-bottom: 1px solid #555555;
}

QMenuBar::item {
    padding: 4px 8px;
}

QMenuBar::item:selected {
    background-color: #0078d4;
}

QMenu {
    background-color: #353535;
    border: 1px solid #555555;
}

QMenu::item {
    padding: 4px 16px;
}

QMenu::item:selected {
    background-color: #0078d4;
}
