#!/usr/bin/env python3
"""
Debug script to test data loading
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.data_manager import DataManager
from core.report_engine import ReportEngine

def debug_data_loading():
    """Debug the data loading process"""
    print("=== Debug Data Loading ===")
    
    # Initialize data manager
    data_manager = DataManager()
    
    # Try to load data
    print("1. Loading all data...")
    success = data_manager.load_all_data()
    print(f"   Load success: {success}")
    
    # Check combined data
    combined_data = data_manager.get_combined_data()
    print(f"2. Combined data shape: {combined_data.shape}")
    
    if not combined_data.empty:
        print(f"   Columns: {list(combined_data.columns)}")
        print(f"   First few rows:")
        print(combined_data.head())
    
    # Check data summary
    summary = data_manager.get_data_summary()
    print(f"3. Data summary: {summary}")
    
    # Check unique users
    users = data_manager.get_unique_users()
    print(f"4. Unique users count: {len(users)}")
    if users:
        print(f"   First few users: {users[:5]}")
    
    # Test report engine
    print("5. Testing report engine...")
    report_engine = ReportEngine(data_manager)
    
    try:
        user_report = report_engine.generate_user_consolidated_report()
        print(f"   User report shape: {user_report.shape}")
        if not user_report.empty:
            print(f"   User report columns: {list(user_report.columns)}")
            print(f"   First few rows:")
            print(user_report.head())
        else:
            print("   User report is empty!")
    except Exception as e:
        print(f"   Error generating user report: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_data_loading()
