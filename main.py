#!/usr/bin/env python3
"""
RingCentral Reports Application
Main entry point for the application
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QIcon

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from gui.splash_screen import SplashScreen
from gui.main_window import MainWindow


def main():
    """Main application entry point"""
    # Create the application
    app = QApplication(sys.argv)
    app.setApplicationName("RingCentral Reports")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("RC Reports")
    
    # Set application icon if available
    # app.setWindowIcon(QIcon("assets/icon.png"))
    
    # Show splash screen first
    splash = SplashScreen()
    splash.show()
    
    # Process events to show splash screen
    app.processEvents()
    
    # Initialize the main window (this will be shown after splash completes)
    main_window = MainWindow()
    
    # Connect splash screen completion to main window
    splash.initialization_complete.connect(lambda: show_main_window(splash, main_window))
    
    # Start the initialization process
    splash.start_initialization()
    
    # Start the event loop
    sys.exit(app.exec())


def show_main_window(splash, main_window):
    """Show main window and close splash screen"""
    splash.close()
    main_window.show()


if __name__ == "__main__":
    main()
