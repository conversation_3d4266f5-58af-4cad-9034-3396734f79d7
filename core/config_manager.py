"""
Configuration Manager for RingCentral Reports Application
Handles application settings, user preferences, and report configurations
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from datetime import datetime


@dataclass
class ReportConfig:
    """Configuration for a saved report"""
    name: str
    description: str
    filters: Dict[str, Any]
    visible_columns: List[str]
    sort_column: Optional[str] = None
    sort_order: str = "asc"
    created_date: str = ""
    modified_date: str = ""


@dataclass
class AppSettings:
    """Application settings"""
    theme: str = "dark"
    default_rows_display: int = 1000
    auto_refresh_on_startup: bool = True
    combine_files_automatically: bool = True
    include_index_in_exports: bool = False
    default_export_format: str = "csv"
    window_geometry: Dict[str, int] = None
    last_data_refresh: str = ""


class ConfigManager:
    """Manages application configuration and settings"""
    
    def __init__(self):
        self.config_dir = Path.home() / "Documents" / "RC Reports" / "config"
        self.settings_file = self.config_dir / "settings.json"
        self.reports_file = self.config_dir / "saved_reports.json"
        self.groups_file = self.config_dir / "user_groups.json"
        
        # Ensure config directory exists
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # Load configurations
        self.app_settings = self.load_app_settings()
        self.saved_reports = self.load_saved_reports()
        self.user_groups = self.load_user_groups()
    
    def load_app_settings(self) -> AppSettings:
        """Load application settings from file"""
        if self.settings_file.exists():
            try:
                with open(self.settings_file, 'r') as f:
                    settings_dict = json.load(f)
                    return AppSettings(**settings_dict)
            except Exception as e:
                print(f"Error loading settings: {e}")
        
        # Return default settings
        return AppSettings()
    
    def save_app_settings(self, settings: AppSettings = None):
        """Save application settings to file"""
        if settings:
            self.app_settings = settings
        
        try:
            with open(self.settings_file, 'w') as f:
                json.dump(asdict(self.app_settings), f, indent=2)
        except Exception as e:
            print(f"Error saving settings: {e}")
    
    def load_saved_reports(self) -> Dict[str, ReportConfig]:
        """Load saved reports from file"""
        if self.reports_file.exists():
            try:
                with open(self.reports_file, 'r') as f:
                    reports_dict = json.load(f)
                    return {name: ReportConfig(**config) for name, config in reports_dict.items()}
            except Exception as e:
                print(f"Error loading saved reports: {e}")
        
        return {}
    
    def save_saved_reports(self):
        """Save reports configuration to file"""
        try:
            reports_dict = {name: asdict(config) for name, config in self.saved_reports.items()}
            with open(self.reports_file, 'w') as f:
                json.dump(reports_dict, f, indent=2)
        except Exception as e:
            print(f"Error saving reports: {e}")
    
    def load_user_groups(self) -> Dict[str, List[str]]:
        """Load user groups from file"""
        if self.groups_file.exists():
            try:
                with open(self.groups_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                print(f"Error loading user groups: {e}")
        
        return {}
    
    def save_user_groups(self, groups: Dict[str, List[str]] = None):
        """Save user groups to file"""
        if groups is not None:
            self.user_groups = groups
        
        try:
            with open(self.groups_file, 'w') as f:
                json.dump(self.user_groups, f, indent=2)
        except Exception as e:
            print(f"Error saving user groups: {e}")
    
    def create_report_config(self, name: str, description: str, filters: Dict[str, Any], 
                           visible_columns: List[str], sort_column: str = None, 
                           sort_order: str = "asc") -> ReportConfig:
        """Create a new report configuration"""
        now = datetime.now().isoformat()
        
        config = ReportConfig(
            name=name,
            description=description,
            filters=filters,
            visible_columns=visible_columns,
            sort_column=sort_column,
            sort_order=sort_order,
            created_date=now,
            modified_date=now
        )
        
        self.saved_reports[name] = config
        self.save_saved_reports()
        
        return config
    
    def update_report_config(self, name: str, **kwargs):
        """Update an existing report configuration"""
        if name in self.saved_reports:
            config = self.saved_reports[name]
            
            # Update specified fields
            for key, value in kwargs.items():
                if hasattr(config, key):
                    setattr(config, key, value)
            
            # Update modified date
            config.modified_date = datetime.now().isoformat()
            
            self.save_saved_reports()
    
    def delete_report_config(self, name: str):
        """Delete a report configuration"""
        if name in self.saved_reports:
            del self.saved_reports[name]
            self.save_saved_reports()
    
    def get_report_config(self, name: str) -> Optional[ReportConfig]:
        """Get a specific report configuration"""
        return self.saved_reports.get(name)
    
    def get_all_report_configs(self) -> Dict[str, ReportConfig]:
        """Get all report configurations"""
        return self.saved_reports.copy()
    
    def create_user_group(self, group_name: str, users: List[str]):
        """Create a new user group"""
        self.user_groups[group_name] = users
        self.save_user_groups()
    
    def update_user_group(self, group_name: str, users: List[str]):
        """Update an existing user group"""
        if group_name in self.user_groups:
            self.user_groups[group_name] = users
            self.save_user_groups()
    
    def delete_user_group(self, group_name: str):
        """Delete a user group"""
        if group_name in self.user_groups:
            del self.user_groups[group_name]
            self.save_user_groups()
    
    def get_user_group(self, group_name: str) -> Optional[List[str]]:
        """Get users in a specific group"""
        return self.user_groups.get(group_name)
    
    def get_all_user_groups(self) -> Dict[str, List[str]]:
        """Get all user groups"""
        return self.user_groups.copy()
    
    def update_setting(self, setting_name: str, value: Any):
        """Update a specific application setting"""
        if hasattr(self.app_settings, setting_name):
            setattr(self.app_settings, setting_name, value)
            self.save_app_settings()
    
    def get_setting(self, setting_name: str, default_value: Any = None) -> Any:
        """Get a specific application setting"""
        return getattr(self.app_settings, setting_name, default_value)
    
    def export_configuration(self, export_path: str):
        """Export all configuration to a file"""
        config_data = {
            "app_settings": asdict(self.app_settings),
            "saved_reports": {name: asdict(config) for name, config in self.saved_reports.items()},
            "user_groups": self.user_groups,
            "export_date": datetime.now().isoformat(),
            "version": "1.0"
        }
        
        try:
            with open(export_path, 'w') as f:
                json.dump(config_data, f, indent=2)
            return True
        except Exception as e:
            print(f"Error exporting configuration: {e}")
            return False
    
    def import_configuration(self, import_path: str, merge: bool = True):
        """Import configuration from a file"""
        try:
            with open(import_path, 'r') as f:
                config_data = json.load(f)
            
            # Import app settings
            if "app_settings" in config_data:
                if merge:
                    # Merge with existing settings
                    for key, value in config_data["app_settings"].items():
                        if hasattr(self.app_settings, key):
                            setattr(self.app_settings, key, value)
                else:
                    # Replace all settings
                    self.app_settings = AppSettings(**config_data["app_settings"])
                
                self.save_app_settings()
            
            # Import saved reports
            if "saved_reports" in config_data:
                if merge:
                    # Merge with existing reports
                    for name, config in config_data["saved_reports"].items():
                        self.saved_reports[name] = ReportConfig(**config)
                else:
                    # Replace all reports
                    self.saved_reports = {name: ReportConfig(**config) 
                                        for name, config in config_data["saved_reports"].items()}
                
                self.save_saved_reports()
            
            # Import user groups
            if "user_groups" in config_data:
                if merge:
                    # Merge with existing groups
                    self.user_groups.update(config_data["user_groups"])
                else:
                    # Replace all groups
                    self.user_groups = config_data["user_groups"]
                
                self.save_user_groups()
            
            return True
            
        except Exception as e:
            print(f"Error importing configuration: {e}")
            return False
    
    def reset_to_defaults(self):
        """Reset all configuration to defaults"""
        self.app_settings = AppSettings()
        self.saved_reports = {}
        self.user_groups = {}
        
        self.save_app_settings()
        self.save_saved_reports()
        self.save_user_groups()
    
    def get_config_summary(self) -> Dict[str, Any]:
        """Get a summary of current configuration"""
        return {
            "settings_file": str(self.settings_file),
            "reports_file": str(self.reports_file),
            "groups_file": str(self.groups_file),
            "saved_reports_count": len(self.saved_reports),
            "user_groups_count": len(self.user_groups),
            "theme": self.app_settings.theme,
            "auto_refresh": self.app_settings.auto_refresh_on_startup,
            "last_refresh": self.app_settings.last_data_refresh
        }
