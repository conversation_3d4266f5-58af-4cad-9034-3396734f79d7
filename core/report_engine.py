"""
Report Engine for RingCentral Reports Application
Handles report generation, filtering, and analysis
"""

import pandas as pd
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import numpy as np


class ReportEngine:
    """Engine for generating and processing reports"""
    
    def __init__(self, data_manager):
        self.data_manager = data_manager
    
    def generate_summary_report(self) -> Dict[str, Any]:
        """Generate a comprehensive summary report"""
        combined_data = self.data_manager.get_combined_data()
        
        if combined_data.empty:
            return {"error": "No data available"}
        
        summary = {
            "total_records": len(combined_data),
            "total_columns": len(combined_data.columns),
            "data_sources": len(self.data_manager.data_frames),
            "unique_users": len(self.data_manager.get_unique_users()),
            "date_range": self._get_date_range(combined_data),
            "column_summary": self._get_column_summary(combined_data),
            "user_activity": self._get_user_activity_summary(combined_data),
            "call_statistics": self._get_call_statistics(combined_data)
        }
        
        return summary
    
    def generate_user_activity_report(self, user_filter: Optional[List[str]] = None) -> pd.DataFrame:
        """Generate user activity report"""
        combined_data = self.data_manager.get_combined_data()
        
        if combined_data.empty:
            return pd.DataFrame()
        
        # Find user-related columns
        user_columns = self._find_user_columns(combined_data)
        
        if not user_columns:
            return pd.DataFrame()
        
        # Create user activity summary
        activity_data = []
        
        for user_col in user_columns:
            unique_users = combined_data[user_col].dropna().unique()
            
            for user in unique_users:
                if user_filter and user not in user_filter:
                    continue
                
                user_data = combined_data[combined_data[user_col] == user]
                
                activity_record = {
                    "user": user,
                    "user_column": user_col,
                    "total_records": len(user_data),
                    "first_activity": self._get_first_date(user_data),
                    "last_activity": self._get_last_date(user_data),
                    "activity_days": self._get_activity_days(user_data)
                }
                
                # Add call-specific metrics if available
                call_metrics = self._get_user_call_metrics(user_data)
                activity_record.update(call_metrics)
                
                activity_data.append(activity_record)
        
        return pd.DataFrame(activity_data)
    
    def generate_call_analytics_report(self) -> pd.DataFrame:
        """Generate call analytics report"""
        combined_data = self.data_manager.get_combined_data()
        
        if combined_data.empty:
            return pd.DataFrame()
        
        # Find call-related columns
        call_columns = self._find_call_columns(combined_data)
        
        if not call_columns:
            return pd.DataFrame()
        
        # Analyze call data
        call_analytics = []
        
        # Group by date if date column exists
        date_columns = self._find_date_columns(combined_data)
        
        if date_columns:
            date_col = date_columns[0]
            combined_data[date_col] = pd.to_datetime(combined_data[date_col], errors='coerce')
            combined_data['date'] = combined_data[date_col].dt.date
            
            daily_stats = combined_data.groupby('date').agg({
                **{col: 'count' for col in call_columns if col in combined_data.columns},
                **{col: 'sum' for col in self._find_duration_columns(combined_data)}
            }).reset_index()
            
            return daily_stats
        
        return pd.DataFrame()
    
    def generate_queue_performance_report(self) -> pd.DataFrame:
        """Generate queue performance report"""
        combined_data = self.data_manager.get_combined_data()
        
        if combined_data.empty:
            return pd.DataFrame()
        
        # Find queue-related columns
        queue_columns = self._find_queue_columns(combined_data)
        
        if not queue_columns:
            return pd.DataFrame()
        
        # Analyze queue performance
        queue_stats = []
        
        for queue_col in queue_columns:
            unique_queues = combined_data[queue_col].dropna().unique()
            
            for queue in unique_queues:
                queue_data = combined_data[combined_data[queue_col] == queue]
                
                stats = {
                    "queue": queue,
                    "total_calls": len(queue_data),
                    "avg_wait_time": self._calculate_avg_wait_time(queue_data),
                    "answered_calls": self._count_answered_calls(queue_data),
                    "abandoned_calls": self._count_abandoned_calls(queue_data),
                    "answer_rate": self._calculate_answer_rate(queue_data)
                }
                
                queue_stats.append(stats)
        
        return pd.DataFrame(queue_stats)
    
    def apply_advanced_filters(self, data: pd.DataFrame, filters: Dict[str, Any]) -> pd.DataFrame:
        """Apply advanced filtering to data"""
        filtered_data = data.copy()
        
        for filter_type, filter_value in filters.items():
            if filter_type == "date_range" and isinstance(filter_value, dict):
                filtered_data = self._apply_date_filter(filtered_data, filter_value)
            elif filter_type == "user_groups" and isinstance(filter_value, list):
                filtered_data = self._apply_user_group_filter(filtered_data, filter_value)
            elif filter_type == "call_types" and isinstance(filter_value, list):
                filtered_data = self._apply_call_type_filter(filtered_data, filter_value)
            elif filter_type == "duration_range" and isinstance(filter_value, dict):
                filtered_data = self._apply_duration_filter(filtered_data, filter_value)
        
        return filtered_data
    
    def _find_user_columns(self, data: pd.DataFrame) -> List[str]:
        """Find columns that likely contain user information"""
        user_keywords = ['user', 'agent', 'name', 'employee', 'extension']
        user_columns = []
        
        for col in data.columns:
            if any(keyword in col.lower() for keyword in user_keywords):
                user_columns.append(col)
        
        return user_columns
    
    def _find_call_columns(self, data: pd.DataFrame) -> List[str]:
        """Find columns that likely contain call information"""
        call_keywords = ['call', 'phone', 'dial', 'ring', 'answer']
        call_columns = []
        
        for col in data.columns:
            if any(keyword in col.lower() for keyword in call_keywords):
                call_columns.append(col)
        
        return call_columns
    
    def _find_date_columns(self, data: pd.DataFrame) -> List[str]:
        """Find columns that likely contain date information"""
        date_keywords = ['date', 'time', 'timestamp', 'created', 'start', 'end']
        date_columns = []
        
        for col in data.columns:
            if any(keyword in col.lower() for keyword in date_keywords):
                date_columns.append(col)
        
        return date_columns
    
    def _find_duration_columns(self, data: pd.DataFrame) -> List[str]:
        """Find columns that likely contain duration information"""
        duration_keywords = ['duration', 'length', 'time', 'seconds', 'minutes']
        duration_columns = []
        
        for col in data.columns:
            if any(keyword in col.lower() for keyword in duration_keywords):
                # Check if column contains numeric data
                if pd.api.types.is_numeric_dtype(data[col]):
                    duration_columns.append(col)
        
        return duration_columns
    
    def _find_queue_columns(self, data: pd.DataFrame) -> List[str]:
        """Find columns that likely contain queue information"""
        queue_keywords = ['queue', 'group', 'department', 'team']
        queue_columns = []
        
        for col in data.columns:
            if any(keyword in col.lower() for keyword in queue_keywords):
                queue_columns.append(col)
        
        return queue_columns
    
    def _get_date_range(self, data: pd.DataFrame) -> Optional[str]:
        """Get the date range from the data"""
        date_columns = self._find_date_columns(data)
        
        for col in date_columns:
            try:
                date_series = pd.to_datetime(data[col], errors='coerce')
                if not date_series.isna().all():
                    min_date = date_series.min()
                    max_date = date_series.max()
                    return f"{min_date.date()} to {max_date.date()}"
            except:
                continue
        
        return None
    
    def _get_column_summary(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Get summary information about columns"""
        summary = {}
        
        for col in data.columns:
            col_info = {
                "type": str(data[col].dtype),
                "non_null_count": data[col].count(),
                "null_count": data[col].isnull().sum(),
                "unique_values": data[col].nunique()
            }
            
            if pd.api.types.is_numeric_dtype(data[col]):
                col_info.update({
                    "mean": data[col].mean(),
                    "min": data[col].min(),
                    "max": data[col].max()
                })
            
            summary[col] = col_info
        
        return summary
    
    def _get_user_activity_summary(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Get user activity summary"""
        user_columns = self._find_user_columns(data)
        
        if not user_columns:
            return {"error": "No user columns found"}
        
        # Use the first user column for summary
        user_col = user_columns[0]
        user_counts = data[user_col].value_counts()
        
        return {
            "total_unique_users": len(user_counts),
            "most_active_user": user_counts.index[0] if len(user_counts) > 0 else None,
            "most_active_user_count": user_counts.iloc[0] if len(user_counts) > 0 else 0,
            "average_activity_per_user": user_counts.mean()
        }
    
    def _get_call_statistics(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Get call statistics summary"""
        call_columns = self._find_call_columns(data)
        duration_columns = self._find_duration_columns(data)
        
        stats = {
            "call_related_columns": len(call_columns),
            "duration_columns": len(duration_columns)
        }
        
        if duration_columns:
            duration_col = duration_columns[0]
            stats.update({
                "total_call_duration": data[duration_col].sum(),
                "average_call_duration": data[duration_col].mean(),
                "max_call_duration": data[duration_col].max(),
                "min_call_duration": data[duration_col].min()
            })
        
        return stats
    
    def _get_first_date(self, data: pd.DataFrame) -> Optional[str]:
        """Get the first date from user data"""
        date_columns = self._find_date_columns(data)
        
        for col in date_columns:
            try:
                date_series = pd.to_datetime(data[col], errors='coerce')
                if not date_series.isna().all():
                    return str(date_series.min().date())
            except:
                continue
        
        return None
    
    def _get_last_date(self, data: pd.DataFrame) -> Optional[str]:
        """Get the last date from user data"""
        date_columns = self._find_date_columns(data)
        
        for col in date_columns:
            try:
                date_series = pd.to_datetime(data[col], errors='coerce')
                if not date_series.isna().all():
                    return str(date_series.max().date())
            except:
                continue
        
        return None
    
    def _get_activity_days(self, data: pd.DataFrame) -> int:
        """Get the number of active days for user"""
        date_columns = self._find_date_columns(data)
        
        for col in date_columns:
            try:
                date_series = pd.to_datetime(data[col], errors='coerce')
                if not date_series.isna().all():
                    unique_dates = date_series.dt.date.nunique()
                    return unique_dates
            except:
                continue
        
        return 0
    
    def _get_user_call_metrics(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Get call metrics for a specific user"""
        metrics = {}
        
        # Count different types of calls
        call_columns = self._find_call_columns(data)
        for col in call_columns:
            metrics[f"{col}_count"] = data[col].count()
        
        # Calculate duration metrics
        duration_columns = self._find_duration_columns(data)
        for col in duration_columns:
            metrics[f"total_{col}"] = data[col].sum()
            metrics[f"avg_{col}"] = data[col].mean()
        
        return metrics
