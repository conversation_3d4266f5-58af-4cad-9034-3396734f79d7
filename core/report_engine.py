"""
Report Engine for RingCentral Reports Application
Handles report generation, filtering, and analysis
"""

import pandas as pd
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import numpy as np


class ReportEngine:
    """Engine for generating and processing reports"""

    def __init__(self, data_manager):
        self.data_manager = data_manager
        self.user_columns_cache = None
        self.call_metrics_cache = None
    
    def generate_user_consolidated_report(self) -> pd.DataFrame:
        """
        Generate a consolidated report with one row per user showing all their metrics
        Each row starts with user name, followed by total answered calls and average handling time
        """
        combined_data = self.data_manager.get_combined_data()

        if combined_data.empty:
            return pd.DataFrame()

        # Find all user-related columns across all data
        user_columns = self._find_user_columns(combined_data)

        if not user_columns:
            return pd.DataFrame()

        # Get all unique users from all user columns
        all_users = set()
        for col in user_columns:
            unique_users = combined_data[col].dropna().unique()
            all_users.update(str(user) for user in unique_users)

        all_users = sorted(list(all_users))

        # Create consolidated report for each user
        user_reports = []

        for user in all_users:
            user_metrics = self._get_user_consolidated_metrics(combined_data, user, user_columns)
            if user_metrics:  # Only add if we found data for this user
                user_reports.append(user_metrics)

        if user_reports:
            df = pd.DataFrame(user_reports)
            # Ensure User Name is the first column
            cols = ['User Name'] + [col for col in df.columns if col != 'User Name']
            df = df[cols]
            return df

        return pd.DataFrame()

    def _get_user_consolidated_metrics(self, data: pd.DataFrame, user: str, user_columns: List[str]) -> Dict[str, Any]:
        """Get consolidated metrics for a specific user across all data sources"""

        # Find all rows that belong to this user
        user_mask = pd.Series([False] * len(data))

        for col in user_columns:
            if col in data.columns:
                user_mask |= (data[col].astype(str) == user)

        user_data = data[user_mask]

        if user_data.empty:
            return None

        metrics = {
            'User Name': user,
            'Total Records': len(user_data),
            'Data Sources': user_data['data_source'].nunique() if 'data_source' in user_data.columns else 1
        }

        # Calculate call metrics
        call_metrics = self._calculate_user_call_metrics(user_data)
        metrics.update(call_metrics)

        # Calculate time-based metrics
        time_metrics = self._calculate_user_time_metrics(user_data)
        metrics.update(time_metrics)

        # Calculate activity metrics
        activity_metrics = self._calculate_user_activity_metrics(user_data)
        metrics.update(activity_metrics)

        return metrics

    def _calculate_user_call_metrics(self, user_data: pd.DataFrame) -> Dict[str, Any]:
        """Calculate call-related metrics for a user"""
        metrics = {
            'Total Answered Calls': 0,
            'Average Handling Time (min)': 0.0,
            'Total Call Duration (min)': 0.0,
            'Longest Call (min)': 0.0,
            'Shortest Call (min)': 0.0
        }

        # Find call-related columns
        answered_columns = self._find_columns_by_keywords(user_data, ['answered', 'completed', 'connected'])
        duration_columns = self._find_columns_by_keywords(user_data, ['duration', 'talk_time', 'handle_time', 'call_time'])

        # Count answered calls
        total_answered = 0
        for col in answered_columns:
            if pd.api.types.is_numeric_dtype(user_data[col]):
                total_answered += user_data[col].sum()
            else:
                # Count non-null, non-empty values
                total_answered += user_data[col].notna().sum()

        metrics['Total Answered Calls'] = int(total_answered)

        # Calculate duration metrics
        total_duration = 0
        duration_values = []

        for col in duration_columns:
            if pd.api.types.is_numeric_dtype(user_data[col]):
                col_values = user_data[col].dropna()
                if not col_values.empty:
                    # Convert seconds to minutes if values seem to be in seconds
                    if col_values.mean() > 300:  # Likely in seconds if average > 5 minutes
                        col_values = col_values / 60

                    duration_values.extend(col_values.tolist())
                    total_duration += col_values.sum()

        if duration_values:
            metrics['Total Call Duration (min)'] = round(total_duration, 2)
            metrics['Average Handling Time (min)'] = round(np.mean(duration_values), 2)
            metrics['Longest Call (min)'] = round(max(duration_values), 2)
            metrics['Shortest Call (min)'] = round(min(duration_values), 2)

        return metrics

    def _calculate_user_time_metrics(self, user_data: pd.DataFrame) -> Dict[str, Any]:
        """Calculate time-based metrics for a user"""
        metrics = {
            'First Activity': 'N/A',
            'Last Activity': 'N/A',
            'Active Days': 0,
            'Total Login Time (hours)': 0.0
        }

        # Find date columns
        date_columns = self._find_date_columns(user_data)

        if date_columns:
            all_dates = []
            for col in date_columns:
                try:
                    dates = pd.to_datetime(user_data[col], errors='coerce').dropna()
                    if not dates.empty:
                        all_dates.extend(dates.tolist())
                except:
                    continue

            if all_dates:
                all_dates = sorted(all_dates)
                metrics['First Activity'] = all_dates[0].strftime('%Y-%m-%d')
                metrics['Last Activity'] = all_dates[-1].strftime('%Y-%m-%d')

                # Count unique days
                unique_dates = set(date.date() for date in all_dates)
                metrics['Active Days'] = len(unique_dates)

        # Find login time columns
        login_columns = self._find_columns_by_keywords(user_data, ['login', 'session', 'online_time', 'work_time'])

        total_login_time = 0
        for col in login_columns:
            if pd.api.types.is_numeric_dtype(user_data[col]):
                col_values = user_data[col].dropna()
                if not col_values.empty:
                    # Convert to hours if values seem to be in minutes or seconds
                    if col_values.mean() > 60:  # Likely in minutes or seconds
                        if col_values.mean() > 3600:  # Likely in seconds
                            total_login_time += col_values.sum() / 3600
                        else:  # Likely in minutes
                            total_login_time += col_values.sum() / 60
                    else:  # Already in hours
                        total_login_time += col_values.sum()

        metrics['Total Login Time (hours)'] = round(total_login_time, 2)

        return metrics

    def _calculate_user_activity_metrics(self, user_data: pd.DataFrame) -> Dict[str, Any]:
        """Calculate activity-based metrics for a user"""
        metrics = {
            'Total Calls Made': 0,
            'Total Calls Received': 0,
            'Missed Calls': 0,
            'Queue Calls Handled': 0
        }

        # Find activity-related columns
        outbound_columns = self._find_columns_by_keywords(user_data, ['outbound', 'made', 'dialed', 'outgoing'])
        inbound_columns = self._find_columns_by_keywords(user_data, ['inbound', 'received', 'incoming'])
        missed_columns = self._find_columns_by_keywords(user_data, ['missed', 'abandoned', 'unanswered'])
        queue_columns = self._find_columns_by_keywords(user_data, ['queue', 'acd', 'group'])

        # Count different types of calls
        for col in outbound_columns:
            if pd.api.types.is_numeric_dtype(user_data[col]):
                metrics['Total Calls Made'] += int(user_data[col].sum())
            else:
                metrics['Total Calls Made'] += int(user_data[col].notna().sum())

        for col in inbound_columns:
            if pd.api.types.is_numeric_dtype(user_data[col]):
                metrics['Total Calls Received'] += int(user_data[col].sum())
            else:
                metrics['Total Calls Received'] += int(user_data[col].notna().sum())

        for col in missed_columns:
            if pd.api.types.is_numeric_dtype(user_data[col]):
                metrics['Missed Calls'] += int(user_data[col].sum())
            else:
                metrics['Missed Calls'] += int(user_data[col].notna().sum())

        for col in queue_columns:
            if pd.api.types.is_numeric_dtype(user_data[col]):
                metrics['Queue Calls Handled'] += int(user_data[col].sum())
            else:
                metrics['Queue Calls Handled'] += int(user_data[col].notna().sum())

        return metrics

    def _find_columns_by_keywords(self, data: pd.DataFrame, keywords: List[str]) -> List[str]:
        """Find columns that contain any of the specified keywords"""
        matching_columns = []

        for col in data.columns:
            col_lower = col.lower()
            if any(keyword.lower() in col_lower for keyword in keywords):
                matching_columns.append(col)

        return matching_columns

    def generate_summary_report(self) -> Dict[str, Any]:
        """Generate a comprehensive summary report"""
        combined_data = self.data_manager.get_combined_data()
        
        if combined_data.empty:
            return {"error": "No data available"}
        
        summary = {
            "total_records": len(combined_data),
            "total_columns": len(combined_data.columns),
            "data_sources": len(self.data_manager.data_frames),
            "unique_users": len(self.data_manager.get_unique_users()),
            "date_range": self._get_date_range(combined_data),
            "column_summary": self._get_column_summary(combined_data),
            "user_activity": self._get_user_activity_summary(combined_data),
            "call_statistics": self._get_call_statistics(combined_data)
        }
        
        return summary
    
    def generate_user_activity_report(self, user_filter: Optional[List[str]] = None) -> pd.DataFrame:
        """Generate user activity report"""
        combined_data = self.data_manager.get_combined_data()
        
        if combined_data.empty:
            return pd.DataFrame()
        
        # Find user-related columns
        user_columns = self._find_user_columns(combined_data)
        
        if not user_columns:
            return pd.DataFrame()
        
        # Create user activity summary
        activity_data = []
        
        for user_col in user_columns:
            unique_users = combined_data[user_col].dropna().unique()
            
            for user in unique_users:
                if user_filter and user not in user_filter:
                    continue
                
                user_data = combined_data[combined_data[user_col] == user]
                
                activity_record = {
                    "user": user,
                    "user_column": user_col,
                    "total_records": len(user_data),
                    "first_activity": self._get_first_date(user_data),
                    "last_activity": self._get_last_date(user_data),
                    "activity_days": self._get_activity_days(user_data)
                }
                
                # Add call-specific metrics if available
                call_metrics = self._get_user_call_metrics(user_data)
                activity_record.update(call_metrics)
                
                activity_data.append(activity_record)
        
        return pd.DataFrame(activity_data)
    
    def generate_call_analytics_report(self) -> pd.DataFrame:
        """Generate call analytics report"""
        combined_data = self.data_manager.get_combined_data()
        
        if combined_data.empty:
            return pd.DataFrame()
        
        # Find call-related columns
        call_columns = self._find_call_columns(combined_data)
        
        if not call_columns:
            return pd.DataFrame()
        
        # Analyze call data
        call_analytics = []
        
        # Group by date if date column exists
        date_columns = self._find_date_columns(combined_data)
        
        if date_columns:
            date_col = date_columns[0]
            combined_data[date_col] = pd.to_datetime(combined_data[date_col], errors='coerce')
            combined_data['date'] = combined_data[date_col].dt.date
            
            daily_stats = combined_data.groupby('date').agg({
                **{col: 'count' for col in call_columns if col in combined_data.columns},
                **{col: 'sum' for col in self._find_duration_columns(combined_data)}
            }).reset_index()
            
            return daily_stats
        
        return pd.DataFrame()
    
    def generate_queue_performance_report(self) -> pd.DataFrame:
        """Generate queue performance report"""
        combined_data = self.data_manager.get_combined_data()
        
        if combined_data.empty:
            return pd.DataFrame()
        
        # Find queue-related columns
        queue_columns = self._find_queue_columns(combined_data)
        
        if not queue_columns:
            return pd.DataFrame()
        
        # Analyze queue performance
        queue_stats = []
        
        for queue_col in queue_columns:
            unique_queues = combined_data[queue_col].dropna().unique()
            
            for queue in unique_queues:
                queue_data = combined_data[combined_data[queue_col] == queue]
                
                stats = {
                    "queue": queue,
                    "total_calls": len(queue_data),
                    "avg_wait_time": self._calculate_avg_wait_time(queue_data),
                    "answered_calls": self._count_answered_calls(queue_data),
                    "abandoned_calls": self._count_abandoned_calls(queue_data),
                    "answer_rate": self._calculate_answer_rate(queue_data)
                }
                
                queue_stats.append(stats)
        
        return pd.DataFrame(queue_stats)
    
    def apply_advanced_filters(self, data: pd.DataFrame, filters: Dict[str, Any]) -> pd.DataFrame:
        """Apply advanced filtering to data"""
        filtered_data = data.copy()
        
        for filter_type, filter_value in filters.items():
            if filter_type == "date_range" and isinstance(filter_value, dict):
                filtered_data = self._apply_date_filter(filtered_data, filter_value)
            elif filter_type == "user_groups" and isinstance(filter_value, list):
                filtered_data = self._apply_user_group_filter(filtered_data, filter_value)
            elif filter_type == "call_types" and isinstance(filter_value, list):
                filtered_data = self._apply_call_type_filter(filtered_data, filter_value)
            elif filter_type == "duration_range" and isinstance(filter_value, dict):
                filtered_data = self._apply_duration_filter(filtered_data, filter_value)
        
        return filtered_data
    
    def _find_user_columns(self, data: pd.DataFrame) -> List[str]:
        """Find columns that likely contain user information"""
        user_keywords = ['user', 'agent', 'name', 'employee', 'extension']
        user_columns = []
        
        for col in data.columns:
            if any(keyword in col.lower() for keyword in user_keywords):
                user_columns.append(col)
        
        return user_columns
    
    def _find_call_columns(self, data: pd.DataFrame) -> List[str]:
        """Find columns that likely contain call information"""
        call_keywords = ['call', 'phone', 'dial', 'ring', 'answer']
        call_columns = []
        
        for col in data.columns:
            if any(keyword in col.lower() for keyword in call_keywords):
                call_columns.append(col)
        
        return call_columns
    
    def _find_date_columns(self, data: pd.DataFrame) -> List[str]:
        """Find columns that likely contain date information"""
        date_keywords = ['date', 'time', 'timestamp', 'created', 'start', 'end']
        date_columns = []
        
        for col in data.columns:
            if any(keyword in col.lower() for keyword in date_keywords):
                date_columns.append(col)
        
        return date_columns
    
    def _find_duration_columns(self, data: pd.DataFrame) -> List[str]:
        """Find columns that likely contain duration information"""
        duration_keywords = ['duration', 'length', 'time', 'seconds', 'minutes']
        duration_columns = []
        
        for col in data.columns:
            if any(keyword in col.lower() for keyword in duration_keywords):
                # Check if column contains numeric data
                if pd.api.types.is_numeric_dtype(data[col]):
                    duration_columns.append(col)
        
        return duration_columns
    
    def _find_queue_columns(self, data: pd.DataFrame) -> List[str]:
        """Find columns that likely contain queue information"""
        queue_keywords = ['queue', 'group', 'department', 'team']
        queue_columns = []
        
        for col in data.columns:
            if any(keyword in col.lower() for keyword in queue_keywords):
                queue_columns.append(col)
        
        return queue_columns
    
    def _get_date_range(self, data: pd.DataFrame) -> Optional[str]:
        """Get the date range from the data"""
        date_columns = self._find_date_columns(data)
        
        for col in date_columns:
            try:
                date_series = pd.to_datetime(data[col], errors='coerce')
                if not date_series.isna().all():
                    min_date = date_series.min()
                    max_date = date_series.max()
                    return f"{min_date.date()} to {max_date.date()}"
            except:
                continue
        
        return None
    
    def _get_column_summary(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Get summary information about columns"""
        summary = {}
        
        for col in data.columns:
            col_info = {
                "type": str(data[col].dtype),
                "non_null_count": data[col].count(),
                "null_count": data[col].isnull().sum(),
                "unique_values": data[col].nunique()
            }
            
            if pd.api.types.is_numeric_dtype(data[col]):
                col_info.update({
                    "mean": data[col].mean(),
                    "min": data[col].min(),
                    "max": data[col].max()
                })
            
            summary[col] = col_info
        
        return summary
    
    def _get_user_activity_summary(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Get user activity summary"""
        user_columns = self._find_user_columns(data)
        
        if not user_columns:
            return {"error": "No user columns found"}
        
        # Use the first user column for summary
        user_col = user_columns[0]
        user_counts = data[user_col].value_counts()
        
        return {
            "total_unique_users": len(user_counts),
            "most_active_user": user_counts.index[0] if len(user_counts) > 0 else None,
            "most_active_user_count": user_counts.iloc[0] if len(user_counts) > 0 else 0,
            "average_activity_per_user": user_counts.mean()
        }
    
    def _get_call_statistics(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Get call statistics summary"""
        call_columns = self._find_call_columns(data)
        duration_columns = self._find_duration_columns(data)
        
        stats = {
            "call_related_columns": len(call_columns),
            "duration_columns": len(duration_columns)
        }
        
        if duration_columns:
            duration_col = duration_columns[0]
            stats.update({
                "total_call_duration": data[duration_col].sum(),
                "average_call_duration": data[duration_col].mean(),
                "max_call_duration": data[duration_col].max(),
                "min_call_duration": data[duration_col].min()
            })
        
        return stats
    
    def _get_first_date(self, data: pd.DataFrame) -> Optional[str]:
        """Get the first date from user data"""
        date_columns = self._find_date_columns(data)
        
        for col in date_columns:
            try:
                date_series = pd.to_datetime(data[col], errors='coerce')
                if not date_series.isna().all():
                    return str(date_series.min().date())
            except:
                continue
        
        return None
    
    def _get_last_date(self, data: pd.DataFrame) -> Optional[str]:
        """Get the last date from user data"""
        date_columns = self._find_date_columns(data)
        
        for col in date_columns:
            try:
                date_series = pd.to_datetime(data[col], errors='coerce')
                if not date_series.isna().all():
                    return str(date_series.max().date())
            except:
                continue
        
        return None
    
    def _get_activity_days(self, data: pd.DataFrame) -> int:
        """Get the number of active days for user"""
        date_columns = self._find_date_columns(data)
        
        for col in date_columns:
            try:
                date_series = pd.to_datetime(data[col], errors='coerce')
                if not date_series.isna().all():
                    unique_dates = date_series.dt.date.nunique()
                    return unique_dates
            except:
                continue
        
        return 0
    
    def _get_user_call_metrics(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Get call metrics for a specific user"""
        metrics = {}
        
        # Count different types of calls
        call_columns = self._find_call_columns(data)
        for col in call_columns:
            metrics[f"{col}_count"] = data[col].count()
        
        # Calculate duration metrics
        duration_columns = self._find_duration_columns(data)
        for col in duration_columns:
            metrics[f"total_{col}"] = data[col].sum()
            metrics[f"avg_{col}"] = data[col].mean()
        
        return metrics
