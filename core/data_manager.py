"""
Data Manager for RingCentral Reports Application
Handles CSV data loading, processing, and management
"""

import pandas as pd
from typing import Dict, List, Optional, Any
from pathlib import Path
from utils.file_utils import FileManager


class DataManager:
    """Manages all data operations for the application"""
    
    def __init__(self):
        self.file_manager = FileManager()
        self.data_frames: Dict[str, pd.DataFrame] = {}
        self.combined_data: Optional[pd.DataFrame] = None
        self.user_groups: Dict[str, List[str]] = {}
        
    def load_all_data(self) -> bool:
        """
        Load all CSV and XLSX files from the RC Reports folder
        Returns True if successful
        """
        try:
            self.data_frames = self.file_manager.load_all_data_files()
            if self.data_frames:
                self.combine_data()
                self.extract_users()
                return True
            return False
        except Exception as e:
            print(f"Error loading data: {e}")
            return False
    
    def combine_data(self):
        """Combine all loaded DataFrames into a single DataFrame"""
        if not self.data_frames:
            self.combined_data = pd.DataFrame()
            return
        
        # Add source file column to each DataFrame
        combined_list = []
        for data_source_name, df in self.data_frames.items():
            df_copy = df.copy()
            df_copy['data_source'] = data_source_name
            combined_list.append(df_copy)
        
        # Combine all DataFrames
        if combined_list:
            self.combined_data = pd.concat(combined_list, ignore_index=True, sort=False)
        else:
            self.combined_data = pd.DataFrame()
    
    def extract_users(self):
        """Extract unique users from the combined data"""
        if self.combined_data is None or self.combined_data.empty:
            return
        
        # Common column names that might contain user information
        user_columns = ['user', 'username', 'name', 'agent', 'agent_name', 
                       'employee', 'extension', 'user_id', 'agent_id']
        
        self.unique_users = set()
        
        for column in self.combined_data.columns:
            if any(user_col in column.lower() for user_col in user_columns):
                unique_values = self.combined_data[column].dropna().unique()
                self.unique_users.update(str(val) for val in unique_values)
        
        self.unique_users = sorted(list(self.unique_users))
    
    def get_data_summary(self) -> Dict[str, Any]:
        """Get summary information about loaded data"""
        summary = {
            'total_files': len(self.data_frames),
            'file_names': list(self.data_frames.keys()),
            'total_rows': 0,
            'total_columns': 0,
            'unique_users': 0,
            'date_range': None
        }
        
        if self.combined_data is not None and not self.combined_data.empty:
            summary['total_rows'] = len(self.combined_data)
            summary['total_columns'] = len(self.combined_data.columns)
            summary['unique_users'] = len(getattr(self, 'unique_users', []))
            
            # Try to find date columns and get date range
            date_columns = [col for col in self.combined_data.columns 
                          if 'date' in col.lower() or 'time' in col.lower()]
            
            if date_columns:
                try:
                    for col in date_columns:
                        date_series = pd.to_datetime(self.combined_data[col], errors='coerce')
                        if not date_series.isna().all():
                            min_date = date_series.min()
                            max_date = date_series.max()
                            summary['date_range'] = f"{min_date.date()} to {max_date.date()}"
                            break
                except:
                    pass
        
        return summary
    
    def get_unique_users(self) -> List[str]:
        """Get list of unique users"""
        return getattr(self, 'unique_users', [])
    
    def get_combined_data(self) -> pd.DataFrame:
        """Get the combined DataFrame"""
        return self.combined_data if self.combined_data is not None else pd.DataFrame()
    
    def get_file_data(self, filename: str) -> Optional[pd.DataFrame]:
        """Get data from a specific file"""
        return self.data_frames.get(filename)
    
    def get_available_columns(self) -> List[str]:
        """Get list of all available columns in combined data"""
        if self.combined_data is not None and not self.combined_data.empty:
            return list(self.combined_data.columns)
        return []
    
    def create_user_group(self, group_name: str, users: List[str]):
        """Create a new user group"""
        self.user_groups[group_name] = users
    
    def get_user_groups(self) -> Dict[str, List[str]]:
        """Get all user groups"""
        return self.user_groups
    
    def delete_user_group(self, group_name: str):
        """Delete a user group"""
        if group_name in self.user_groups:
            del self.user_groups[group_name]
    
    def filter_data(self, filters: Dict[str, Any]) -> pd.DataFrame:
        """
        Filter the combined data based on provided filters
        filters: Dictionary with column names as keys and filter values as values
        """
        if self.combined_data is None or self.combined_data.empty:
            return pd.DataFrame()
        
        filtered_data = self.combined_data.copy()
        
        for column, filter_value in filters.items():
            if column in filtered_data.columns:
                if isinstance(filter_value, list):
                    # Multiple values filter
                    filtered_data = filtered_data[filtered_data[column].isin(filter_value)]
                elif isinstance(filter_value, str):
                    # String contains filter
                    filtered_data = filtered_data[filtered_data[column].str.contains(
                        filter_value, case=False, na=False)]
                else:
                    # Exact match filter
                    filtered_data = filtered_data[filtered_data[column] == filter_value]
        
        return filtered_data
